import React from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  department: string;
  position: string;
  hireDate: string;
  salary: number;
  photoPath?: string;
  isActive: boolean;
}



interface Props {
  employee: Employee;
  departments: string[];
  errors?: Record<string, string[]>;
  error?: string;
}

const EmployeeEdit: React.FC<Props> = ({ employee, departments, errors, error }) => {
  const { data, setData, post, processing } = useForm({
    firstName: employee.firstName,
    lastName: employee.lastName,
    email: employee.email,
    phoneNumber: employee.phoneNumber || '',
    department: employee.department,
    position: employee.position,
    hireDate: employee.hireDate.split('T')[0], // Convert to YYYY-MM-DD format
    salary: employee.salary,
    photoPath: employee.photoPath || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(`/employees/${employee.id}/edit`);
  };

  const getFieldError = (fieldName: string): string | undefined => {
    return errors?.[fieldName]?.[0];
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/employees">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Employees
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Employee</h1>
          <p className="text-gray-600 mt-1">Update {employee.firstName} {employee.lastName}'s information</p>
        </div>
      </div>

      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Employee Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={data.firstName}
                  onChange={(e) => setData('firstName', e.target.value)}
                  className={getFieldError('firstName') ? 'border-red-500' : ''}
                  placeholder="Enter first name"
                />
                {getFieldError('firstName') && (
                  <p className="text-sm text-red-600">{getFieldError('firstName')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={data.lastName}
                  onChange={(e) => setData('lastName', e.target.value)}
                  className={getFieldError('lastName') ? 'border-red-500' : ''}
                  placeholder="Enter last name"
                />
                {getFieldError('lastName') && (
                  <p className="text-sm text-red-600">{getFieldError('lastName')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  className={getFieldError('email') ? 'border-red-500' : ''}
                  placeholder="Enter email address"
                />
                {getFieldError('email') && (
                  <p className="text-sm text-red-600">{getFieldError('email')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  value={data.phoneNumber}
                  onChange={(e) => setData('phoneNumber', e.target.value)}
                  className={getFieldError('phoneNumber') ? 'border-red-500' : ''}
                  placeholder="Enter phone number"
                />
                {getFieldError('phoneNumber') && (
                  <p className="text-sm text-red-600">{getFieldError('phoneNumber')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Department *</Label>
                <Select value={data.department} onValueChange={(value) => setData('department', value)}>
                  <SelectTrigger className={getFieldError('department') ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                    <SelectItem value="Human Resources">Human Resources</SelectItem>
                    <SelectItem value="Engineering">Engineering</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Sales">Sales</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Operations">Operations</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError('department') && (
                  <p className="text-sm text-red-600">{getFieldError('department')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Position *</Label>
                <Input
                  id="position"
                  value={data.position}
                  onChange={(e) => setData('position', e.target.value)}
                  className={getFieldError('position') ? 'border-red-500' : ''}
                  placeholder="Enter job position"
                />
                {getFieldError('position') && (
                  <p className="text-sm text-red-600">{getFieldError('position')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="hireDate">Hire Date *</Label>
                <Input
                  id="hireDate"
                  type="date"
                  value={data.hireDate}
                  onChange={(e) => setData('hireDate', e.target.value)}
                  className={getFieldError('hireDate') ? 'border-red-500' : ''}
                />
                {getFieldError('hireDate') && (
                  <p className="text-sm text-red-600">{getFieldError('hireDate')}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="salary">Salary *</Label>
                <Input
                  id="salary"
                  type="number"
                  step="0.01"
                  min="0"
                  value={data.salary}
                  onChange={(e) => setData('salary', parseFloat(e.target.value) || 0)}
                  className={getFieldError('salary') ? 'border-red-500' : ''}
                  placeholder="Enter salary amount"
                />
                {getFieldError('salary') && (
                  <p className="text-sm text-red-600">{getFieldError('salary')}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="photoPath">Photo URL</Label>
              <Input
                id="photoPath"
                value={data.photoPath}
                onChange={(e) => setData('photoPath', e.target.value)}
                className={getFieldError('photoPath') ? 'border-red-500' : ''}
                placeholder="Enter photo URL (optional)"
              />
              {getFieldError('photoPath') && (
                <p className="text-sm text-red-600">{getFieldError('photoPath')}</p>
              )}
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t">
              <Link href="/employees">
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </Link>
              <Button type="submit" disabled={processing}>
                <Save className="w-4 h-4 mr-2" />
                {processing ? 'Updating...' : 'Update Employee'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeEdit;
