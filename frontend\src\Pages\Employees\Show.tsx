import React from 'react';
import { Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  Calendar, 
  DollarSign, 
  Building, 
  UserCheck, 
  UserX 
} from 'lucide-react';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  phoneNumber?: string;
  department: string;
  position: string;
  hireDate: string;
  salary: number;
  photoPath?: string;
  isActive: boolean;
  creationTime: string;
  lastModificationTime?: string;
}

interface Permissions {
  edit: boolean;
  delete: boolean;
  manageActivation: boolean;
}

interface Props {
  employee: Employee;
  permissions: Permissions;
}

const EmployeeShow: React.FC<Props> = ({ employee, permissions }) => {
  const handleDelete = () => {
    router.post(`/employees/${employee.id}/delete`);
  };

  const handleActivate = () => {
    router.post(`/employees/${employee.id}/activate`);
  };

  const handleDeactivate = () => {
    router.post(`/employees/${employee.id}/deactivate`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const calculateYearsOfService = (hireDate: string) => {
    const hire = new Date(hireDate);
    const now = new Date();
    const years = now.getFullYear() - hire.getFullYear();
    const monthDiff = now.getMonth() - hire.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < hire.getDate())) {
      return years - 1;
    }
    return years;
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/employees">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Employees
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{employee.fullName}</h1>
            <p className="text-gray-600 mt-1">{employee.position} • {employee.department}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          {permissions.manageActivation && (
            <Button
              variant="outline"
              onClick={employee.isActive ? handleDeactivate : handleActivate}
            >
              {employee.isActive ? (
                <>
                  <UserX className="w-4 h-4 mr-2" />
                  Deactivate
                </>
              ) : (
                <>
                  <UserCheck className="w-4 h-4 mr-2" />
                  Activate
                </>
              )}
            </Button>
          )}
          {permissions.edit && (
            <Link href={`/employees/${employee.id}/edit`}>
              <Button variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Link>
          )}
          {permissions.delete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Employee</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete {employee.fullName}? 
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="lg:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="w-24 h-24">
                <AvatarImage src={employee.photoPath} alt={employee.fullName} />
                <AvatarFallback className="text-lg">
                  {getInitials(employee.firstName, employee.lastName)}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-xl">{employee.fullName}</CardTitle>
            <div className="flex justify-center">
              <Badge variant={employee.isActive ? "default" : "secondary"}>
                {employee.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3 text-sm">
              <Mail className="w-4 h-4 text-gray-500" />
              <a href={`mailto:${employee.email}`} className="text-blue-600 hover:underline">
                {employee.email}
              </a>
            </div>
            {employee.phoneNumber && (
              <div className="flex items-center gap-3 text-sm">
                <Phone className="w-4 h-4 text-gray-500" />
                <a href={`tel:${employee.phoneNumber}`} className="text-blue-600 hover:underline">
                  {employee.phoneNumber}
                </a>
              </div>
            )}
            <div className="flex items-center gap-3 text-sm">
              <Building className="w-4 h-4 text-gray-500" />
              <span>{employee.department}</span>
            </div>
          </CardContent>
        </Card>

        {/* Details Card */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Employee Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Position</label>
                  <p className="text-lg font-semibold">{employee.position}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Department</label>
                  <p className="text-lg">{employee.department}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Employee ID</label>
                  <p className="text-sm font-mono text-gray-600">{employee.id}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Salary</label>
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <p className="text-lg font-semibold text-green-600">
                      {formatCurrency(employee.salary)}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Hire Date</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <p className="text-lg">{formatDate(employee.hireDate)}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Years of Service</label>
                  <p className="text-lg font-semibold">
                    {calculateYearsOfService(employee.hireDate)} years
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Information Card */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div>
                <label className="font-medium text-gray-500">Created</label>
                <p>{formatDate(employee.creationTime)}</p>
              </div>
              {employee.lastModificationTime && (
                <div>
                  <label className="font-medium text-gray-500">Last Modified</label>
                  <p>{formatDate(employee.lastModificationTime)}</p>
                </div>
              )}
              <div>
                <label className="font-medium text-gray-500">Status</label>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={employee.isActive ? "default" : "secondary"}>
                    {employee.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EmployeeShow;
