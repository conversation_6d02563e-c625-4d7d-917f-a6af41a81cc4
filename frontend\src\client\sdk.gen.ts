// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';
import type { GetApiAbpApiDefinitionData, GetApiAbpApiDefinitionResponses, GetApiAbpApiDefinitionErrors, GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpApplicationLocalizationData, GetApiAbpApplicationLocalizationResponses, GetApiAbpApplicationLocalizationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, GetApiSettingManagementEmailingData, GetApiSettingManagementEmailingResponses, GetApiSettingManagementEmailingErrors, PostApiSettingManagementEmailingData, PostApiSettingManagementEmailingResponses, PostApiSettingManagementEmailingErrors, PostApiSettingManagementEmailingSendTestEmailData, PostApiSettingManagementEmailingSendTestEmailResponses, PostApiSettingManagementEmailingSendTestEmailErrors, GetApiAppEmployeeData, GetApiAppEmployeeResponses, GetApiAppEmployeeErrors, PostApiAppEmployeeData, PostApiAppEmployeeResponses, PostApiAppEmployeeErrors, DeleteApiAppEmployeeByIdData, DeleteApiAppEmployeeByIdResponses, DeleteApiAppEmployeeByIdErrors, GetApiAppEmployeeByIdData, GetApiAppEmployeeByIdResponses, GetApiAppEmployeeByIdErrors, PutApiAppEmployeeByIdData, PutApiAppEmployeeByIdResponses, PutApiAppEmployeeByIdErrors, GetApiAppEmployeeDepartmentsData, GetApiAppEmployeeDepartmentsResponses, GetApiAppEmployeeDepartmentsErrors, PostApiAppEmployeeIsEmailUniqueData, PostApiAppEmployeeIsEmailUniqueResponses, PostApiAppEmployeeIsEmailUniqueErrors, DeleteApiAppEmployeeMultipleData, DeleteApiAppEmployeeMultipleResponses, DeleteApiAppEmployeeMultipleErrors, PostApiAppEmployeeExportToExcelData, PostApiAppEmployeeExportToExcelResponses, PostApiAppEmployeeExportToExcelErrors, PostApiAppEmployeeByIdActivateData, PostApiAppEmployeeByIdActivateResponses, PostApiAppEmployeeByIdActivateErrors, PostApiAppEmployeeByIdDeactivateData, PostApiAppEmployeeByIdDeactivateResponses, PostApiAppEmployeeByIdDeactivateErrors, DeleteApiFeatureManagementFeaturesData, DeleteApiFeatureManagementFeaturesResponses, DeleteApiFeatureManagementFeaturesErrors, GetApiFeatureManagementFeaturesData, GetApiFeatureManagementFeaturesResponses, GetApiFeatureManagementFeaturesErrors, PutApiFeatureManagementFeaturesData, PutApiFeatureManagementFeaturesResponses, PutApiFeatureManagementFeaturesErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, GetApiPermissionManagementPermissionsData, GetApiPermissionManagementPermissionsResponses, GetApiPermissionManagementPermissionsErrors, PutApiPermissionManagementPermissionsData, PutApiPermissionManagementPermissionsResponses, PutApiPermissionManagementPermissionsErrors, GetApiAppProductData, GetApiAppProductResponses, GetApiAppProductErrors, PostApiAppProductData, PostApiAppProductResponses, PostApiAppProductErrors, DeleteApiAppProductByIdData, DeleteApiAppProductByIdResponses, DeleteApiAppProductByIdErrors, GetApiAppProductByIdData, GetApiAppProductByIdResponses, GetApiAppProductByIdErrors, PutApiAppProductByIdData, PutApiAppProductByIdResponses, PutApiAppProductByIdErrors, PostApiAppProductIsSkuInUseData, PostApiAppProductIsSkuInUseResponses, PostApiAppProductIsSkuInUseErrors, PutApiAppProductByIdStockQuantityData, PutApiAppProductByIdStockQuantityResponses, PutApiAppProductByIdStockQuantityErrors, GetApiProductsData, GetApiProductsResponses, GetApiProductsErrors, PostApiProductsData, PostApiProductsResponses, PostApiProductsErrors, DeleteApiProductsByIdData, DeleteApiProductsByIdResponses, DeleteApiProductsByIdErrors, GetApiProductsByIdData, GetApiProductsByIdResponses, GetApiProductsByIdErrors, PutApiProductsByIdData, PutApiProductsByIdResponses, PutApiProductsByIdErrors, GetApiProductsCheckSkuBySkuData, GetApiProductsCheckSkuBySkuResponses, GetApiProductsCheckSkuBySkuErrors, PutApiProductsByIdStockData, PutApiProductsByIdStockResponses, PutApiProductsByIdStockErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, GetApiIdentityRolesAllData, GetApiIdentityRolesAllResponses, GetApiIdentityRolesAllErrors, GetApiIdentityRolesData, GetApiIdentityRolesResponses, GetApiIdentityRolesErrors, PostApiIdentityRolesData, PostApiIdentityRolesResponses, PostApiIdentityRolesErrors, DeleteApiIdentityRolesByIdData, DeleteApiIdentityRolesByIdResponses, DeleteApiIdentityRolesByIdErrors, GetApiIdentityRolesByIdData, GetApiIdentityRolesByIdResponses, GetApiIdentityRolesByIdErrors, PutApiIdentityRolesByIdData, PutApiIdentityRolesByIdResponses, PutApiIdentityRolesByIdErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiSettingManagementTimezoneData, GetApiSettingManagementTimezoneResponses, GetApiSettingManagementTimezoneErrors, PostApiSettingManagementTimezoneData, PostApiSettingManagementTimezoneResponses, PostApiSettingManagementTimezoneErrors, GetApiSettingManagementTimezoneTimezonesData, GetApiSettingManagementTimezoneTimezonesResponses, GetApiSettingManagementTimezoneTimezonesErrors, DeleteApiIdentityUsersByIdData, DeleteApiIdentityUsersByIdResponses, DeleteApiIdentityUsersByIdErrors, GetApiIdentityUsersByIdData, GetApiIdentityUsersByIdResponses, GetApiIdentityUsersByIdErrors, PutApiIdentityUsersByIdData, PutApiIdentityUsersByIdResponses, PutApiIdentityUsersByIdErrors, GetApiIdentityUsersData, GetApiIdentityUsersResponses, GetApiIdentityUsersErrors, PostApiIdentityUsersData, PostApiIdentityUsersResponses, PostApiIdentityUsersErrors, GetApiIdentityUsersByIdRolesData, GetApiIdentityUsersByIdRolesResponses, GetApiIdentityUsersByIdRolesErrors, PutApiIdentityUsersByIdRolesData, PutApiIdentityUsersByIdRolesResponses, PutApiIdentityUsersByIdRolesErrors, GetApiIdentityUsersAssignableRolesData, GetApiIdentityUsersAssignableRolesResponses, GetApiIdentityUsersAssignableRolesErrors, GetApiIdentityUsersByUsernameByUserNameData, GetApiIdentityUsersByUsernameByUserNameResponses, GetApiIdentityUsersByUsernameByUserNameErrors, GetApiIdentityUsersByEmailByEmailData, GetApiIdentityUsersByEmailByEmailResponses, GetApiIdentityUsersByEmailByEmailErrors, GetApiIdentityUsersLookupByIdData, GetApiIdentityUsersLookupByIdResponses, GetApiIdentityUsersLookupByIdErrors, GetApiIdentityUsersLookupByUsernameByUserNameData, GetApiIdentityUsersLookupByUsernameByUserNameResponses, GetApiIdentityUsersLookupByUsernameByUserNameErrors, GetApiIdentityUsersLookupSearchData, GetApiIdentityUsersLookupSearchResponses, GetApiIdentityUsersLookupSearchErrors, GetApiIdentityUsersLookupCountData, GetApiIdentityUsersLookupCountResponses, GetApiIdentityUsersLookupCountErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAbpApiDefinition = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApiDefinitionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApiDefinitionResponses, GetApiAbpApiDefinitionErrors, ThrowOnError>({
        url: '/api/abp/api-definition',
        ...options
    });
};

export const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({
        url: '/api/abp/application-configuration',
        ...options
    });
};

export const getApiAbpApplicationLocalization = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpApplicationLocalizationData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpApplicationLocalizationResponses, GetApiAbpApplicationLocalizationErrors, ThrowOnError>({
        url: '/api/abp/application-localization',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',
        ...options
    });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({
        url: '/api/account/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({
        url: '/api/account/send-password-reset-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({
        url: '/api/account/verify-password-reset-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({
        url: '/api/account/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({
        url: '/api/account/dynamic-claims/refresh',
        ...options
    });
};

export const getApiSettingManagementEmailing = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementEmailingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementEmailingResponses, GetApiSettingManagementEmailingErrors, ThrowOnError>({
        url: '/api/setting-management/emailing',
        ...options
    });
};

export const postApiSettingManagementEmailing = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementEmailingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementEmailingResponses, PostApiSettingManagementEmailingErrors, ThrowOnError>({
        url: '/api/setting-management/emailing',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiSettingManagementEmailingSendTestEmail = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementEmailingSendTestEmailData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementEmailingSendTestEmailResponses, PostApiSettingManagementEmailingSendTestEmailErrors, ThrowOnError>({
        url: '/api/setting-management/emailing/send-test-email',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAppEmployee = <ThrowOnError extends boolean = false>(options?: Options<GetApiAppEmployeeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAppEmployeeResponses, GetApiAppEmployeeErrors, ThrowOnError>({
        url: '/api/app/employee',
        ...options
    });
};

export const postApiAppEmployee = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppEmployeeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppEmployeeResponses, PostApiAppEmployeeErrors, ThrowOnError>({
        url: '/api/app/employee',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiAppEmployeeById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiAppEmployeeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiAppEmployeeByIdResponses, DeleteApiAppEmployeeByIdErrors, ThrowOnError>({
        url: '/api/app/employee/{id}',
        ...options
    });
};

export const getApiAppEmployeeById = <ThrowOnError extends boolean = false>(options: Options<GetApiAppEmployeeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAppEmployeeByIdResponses, GetApiAppEmployeeByIdErrors, ThrowOnError>({
        url: '/api/app/employee/{id}',
        ...options
    });
};

export const putApiAppEmployeeById = <ThrowOnError extends boolean = false>(options: Options<PutApiAppEmployeeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiAppEmployeeByIdResponses, PutApiAppEmployeeByIdErrors, ThrowOnError>({
        url: '/api/app/employee/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiAppEmployeeDepartments = <ThrowOnError extends boolean = false>(options?: Options<GetApiAppEmployeeDepartmentsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAppEmployeeDepartmentsResponses, GetApiAppEmployeeDepartmentsErrors, ThrowOnError>({
        url: '/api/app/employee/departments',
        ...options
    });
};

export const postApiAppEmployeeIsEmailUnique = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppEmployeeIsEmailUniqueData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppEmployeeIsEmailUniqueResponses, PostApiAppEmployeeIsEmailUniqueErrors, ThrowOnError>({
        url: '/api/app/employee/is-email-unique',
        ...options
    });
};

export const deleteApiAppEmployeeMultiple = <ThrowOnError extends boolean = false>(options?: Options<DeleteApiAppEmployeeMultipleData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteApiAppEmployeeMultipleResponses, DeleteApiAppEmployeeMultipleErrors, ThrowOnError>({
        url: '/api/app/employee/multiple',
        ...options
    });
};

export const postApiAppEmployeeExportToExcel = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppEmployeeExportToExcelData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppEmployeeExportToExcelResponses, PostApiAppEmployeeExportToExcelErrors, ThrowOnError>({
        url: '/api/app/employee/export-to-excel',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAppEmployeeByIdActivate = <ThrowOnError extends boolean = false>(options: Options<PostApiAppEmployeeByIdActivateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiAppEmployeeByIdActivateResponses, PostApiAppEmployeeByIdActivateErrors, ThrowOnError>({
        url: '/api/app/employee/{id}/activate',
        ...options
    });
};

export const postApiAppEmployeeByIdDeactivate = <ThrowOnError extends boolean = false>(options: Options<PostApiAppEmployeeByIdDeactivateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiAppEmployeeByIdDeactivateResponses, PostApiAppEmployeeByIdDeactivateErrors, ThrowOnError>({
        url: '/api/app/employee/{id}/deactivate',
        ...options
    });
};

export const deleteApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<DeleteApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteApiFeatureManagementFeaturesResponses, DeleteApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options
    });
};

export const getApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<GetApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiFeatureManagementFeaturesResponses, GetApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options
    });
};

export const putApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<PutApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiFeatureManagementFeaturesResponses, PutApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({
        url: '/api/account/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({
        url: '/api/account/logout',
        ...options
    });
};

export const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({
        url: '/api/account/check-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPermissionManagementPermissions = <ThrowOnError extends boolean = false>(options?: Options<GetApiPermissionManagementPermissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPermissionManagementPermissionsResponses, GetApiPermissionManagementPermissionsErrors, ThrowOnError>({
        url: '/api/permission-management/permissions',
        ...options
    });
};

export const putApiPermissionManagementPermissions = <ThrowOnError extends boolean = false>(options?: Options<PutApiPermissionManagementPermissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiPermissionManagementPermissionsResponses, PutApiPermissionManagementPermissionsErrors, ThrowOnError>({
        url: '/api/permission-management/permissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAppProduct = <ThrowOnError extends boolean = false>(options?: Options<GetApiAppProductData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAppProductResponses, GetApiAppProductErrors, ThrowOnError>({
        url: '/api/app/product',
        ...options
    });
};

export const postApiAppProduct = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppProductData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppProductResponses, PostApiAppProductErrors, ThrowOnError>({
        url: '/api/app/product',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiAppProductById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiAppProductByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiAppProductByIdResponses, DeleteApiAppProductByIdErrors, ThrowOnError>({
        url: '/api/app/product/{id}',
        ...options
    });
};

export const getApiAppProductById = <ThrowOnError extends boolean = false>(options: Options<GetApiAppProductByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAppProductByIdResponses, GetApiAppProductByIdErrors, ThrowOnError>({
        url: '/api/app/product/{id}',
        ...options
    });
};

export const putApiAppProductById = <ThrowOnError extends boolean = false>(options: Options<PutApiAppProductByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiAppProductByIdResponses, PutApiAppProductByIdErrors, ThrowOnError>({
        url: '/api/app/product/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiAppProductIsSkuInUse = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppProductIsSkuInUseData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppProductIsSkuInUseResponses, PostApiAppProductIsSkuInUseErrors, ThrowOnError>({
        url: '/api/app/product/is-sku-in-use',
        ...options
    });
};

export const putApiAppProductByIdStockQuantity = <ThrowOnError extends boolean = false>(options: Options<PutApiAppProductByIdStockQuantityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiAppProductByIdStockQuantityResponses, PutApiAppProductByIdStockQuantityErrors, ThrowOnError>({
        url: '/api/app/product/{id}/stock-quantity',
        ...options
    });
};

export const getApiProducts = <ThrowOnError extends boolean = false>(options?: Options<GetApiProductsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiProductsResponses, GetApiProductsErrors, ThrowOnError>({
        url: '/api/products',
        ...options
    });
};

export const postApiProducts = <ThrowOnError extends boolean = false>(options?: Options<PostApiProductsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProductsResponses, PostApiProductsErrors, ThrowOnError>({
        url: '/api/products',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiProductsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiProductsByIdResponses, DeleteApiProductsByIdErrors, ThrowOnError>({
        url: '/api/products/{id}',
        ...options
    });
};

export const getApiProductsById = <ThrowOnError extends boolean = false>(options: Options<GetApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProductsByIdResponses, GetApiProductsByIdErrors, ThrowOnError>({
        url: '/api/products/{id}',
        ...options
    });
};

export const putApiProductsById = <ThrowOnError extends boolean = false>(options: Options<PutApiProductsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiProductsByIdResponses, PutApiProductsByIdErrors, ThrowOnError>({
        url: '/api/products/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiProductsCheckSkuBySku = <ThrowOnError extends boolean = false>(options: Options<GetApiProductsCheckSkuBySkuData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProductsCheckSkuBySkuResponses, GetApiProductsCheckSkuBySkuErrors, ThrowOnError>({
        url: '/api/products/check-sku/{sku}',
        ...options
    });
};

export const putApiProductsByIdStock = <ThrowOnError extends boolean = false>(options: Options<PutApiProductsByIdStockData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiProductsByIdStockResponses, PutApiProductsByIdStockErrors, ThrowOnError>({
        url: '/api/products/{id}/stock',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options
    });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({
        url: '/api/account/my-profile/change-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityRolesAll = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityRolesAllData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityRolesAllResponses, GetApiIdentityRolesAllErrors, ThrowOnError>({
        url: '/api/identity/roles/all',
        ...options
    });
};

export const getApiIdentityRoles = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityRolesResponses, GetApiIdentityRolesErrors, ThrowOnError>({
        url: '/api/identity/roles',
        ...options
    });
};

export const postApiIdentityRoles = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityRolesResponses, PostApiIdentityRolesErrors, ThrowOnError>({
        url: '/api/identity/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityRolesByIdResponses, DeleteApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options
    });
};

export const getApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityRolesByIdResponses, GetApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options
    });
};

export const putApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityRolesByIdResponses, PutApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options
    });
};

export const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiSettingManagementTimezone = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementTimezoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementTimezoneResponses, GetApiSettingManagementTimezoneErrors, ThrowOnError>({
        url: '/api/setting-management/timezone',
        ...options
    });
};

export const postApiSettingManagementTimezone = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementTimezoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementTimezoneResponses, PostApiSettingManagementTimezoneErrors, ThrowOnError>({
        url: '/api/setting-management/timezone',
        ...options
    });
};

export const getApiSettingManagementTimezoneTimezones = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementTimezoneTimezonesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementTimezoneTimezonesResponses, GetApiSettingManagementTimezoneTimezonesErrors, ThrowOnError>({
        url: '/api/setting-management/timezone/timezones',
        ...options
    });
};

export const deleteApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityUsersByIdResponses, DeleteApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options
    });
};

export const getApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByIdResponses, GetApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options
    });
};

export const putApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityUsersByIdResponses, PutApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiIdentityUsers = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersResponses, GetApiIdentityUsersErrors, ThrowOnError>({
        url: '/api/identity/users',
        ...options
    });
};

export const postApiIdentityUsers = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityUsersResponses, PostApiIdentityUsersErrors, ThrowOnError>({
        url: '/api/identity/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityUsersByIdRoles = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByIdRolesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByIdRolesResponses, GetApiIdentityUsersByIdRolesErrors, ThrowOnError>({
        url: '/api/identity/users/{id}/roles',
        ...options
    });
};

export const putApiIdentityUsersByIdRoles = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityUsersByIdRolesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityUsersByIdRolesResponses, PutApiIdentityUsersByIdRolesErrors, ThrowOnError>({
        url: '/api/identity/users/{id}/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiIdentityUsersAssignableRoles = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersAssignableRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersAssignableRolesResponses, GetApiIdentityUsersAssignableRolesErrors, ThrowOnError>({
        url: '/api/identity/users/assignable-roles',
        ...options
    });
};

export const getApiIdentityUsersByUsernameByUserName = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByUsernameByUserNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByUsernameByUserNameResponses, GetApiIdentityUsersByUsernameByUserNameErrors, ThrowOnError>({
        url: '/api/identity/users/by-username/{userName}',
        ...options
    });
};

export const getApiIdentityUsersByEmailByEmail = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByEmailByEmailData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByEmailByEmailResponses, GetApiIdentityUsersByEmailByEmailErrors, ThrowOnError>({
        url: '/api/identity/users/by-email/{email}',
        ...options
    });
};

export const getApiIdentityUsersLookupById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersLookupByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersLookupByIdResponses, GetApiIdentityUsersLookupByIdErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/{id}',
        ...options
    });
};

export const getApiIdentityUsersLookupByUsernameByUserName = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersLookupByUsernameByUserNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersLookupByUsernameByUserNameResponses, GetApiIdentityUsersLookupByUsernameByUserNameErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/by-username/{userName}',
        ...options
    });
};

export const getApiIdentityUsersLookupSearch = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersLookupSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersLookupSearchResponses, GetApiIdentityUsersLookupSearchErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/search',
        ...options
    });
};

export const getApiIdentityUsersLookupCount = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersLookupCountData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersLookupCountResponses, GetApiIdentityUsersLookupCountErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/count',
        ...options
    });
};