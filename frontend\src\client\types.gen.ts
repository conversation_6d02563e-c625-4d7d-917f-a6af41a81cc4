// This file is auto-generated by @hey-api/openapi-ts

export type ImipEkbEmployeesCreateUpdateEmployeeDto = {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string | null;
    department: string;
    position?: string | null;
    hireDate: string;
    salary: number;
    photoPath?: string | null;
};

export type ImipEkbEmployeesEmployeeDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    fullName?: string | null;
    email?: string | null;
    phoneNumber?: string | null;
    department?: string | null;
    position?: string | null;
    hireDate?: string;
    salary?: number;
    photoPath?: string | null;
    isActive?: boolean;
};

export type ImipEkbEmployeesGetEmployeeListDto = {
    maxResultCount?: number;
    skipCount?: number;
    sorting?: string | null;
    filter?: string | null;
    department?: string | null;
    isActive?: boolean | null;
};

export type ImipEkbProductsCreateUpdateProductDto = {
    name: string;
    description?: string | null;
    price: number;
    stockQuantity: number;
    sku: string;
    isActive?: boolean;
};

export type ImipEkbProductsProductDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    name?: string | null;
    description?: string | null;
    price?: number;
    stockQuantity?: number;
    sku?: string | null;
    isActive?: boolean;
};

export type VoloAbpAccountChangePasswordInput = {
    currentPassword?: string | null;
    newPassword: string;
};

export type VoloAbpAccountProfileDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type VoloAbpAccountProfileDtoWritable = {
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type VoloAbpAccountRegisterDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type VoloAbpAccountRegisterDtoWritable = {
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type VoloAbpAccountResetPasswordDto = {
    userId?: string;
    resetToken: string;
    password: string;
};

export type VoloAbpAccountSendPasswordResetCodeDto = {
    email: string;
    appName: string;
    returnUrl?: string | null;
    returnUrlHash?: string | null;
};

export type VoloAbpAccountUpdateProfileDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpAccountUpdateProfileDtoWritable = {
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpAccountVerifyPasswordResetTokenInput = {
    userId?: string;
    resetToken: string;
};

export type VoloAbpAccountWebAreasAccountControllersModelsAbpLoginResult = {
    result?: VoloAbpAccountWebAreasAccountControllersModelsLoginResultType;
    readonly description?: string | null;
};

export type VoloAbpAccountWebAreasAccountControllersModelsLoginResultType = 1 | 2 | 3 | 4 | 5;

export type VoloAbpAccountWebAreasAccountControllersModelsUserLoginInfo = {
    userNameOrEmailAddress: string;
    password: string;
    rememberMe?: boolean;
};

export type VoloAbpApplicationDtosListResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null = {
    items?: Array<VoloAbpIdentityIdentityRoleDto> | null;
};

export type VoloAbpApplicationDtosListResultDto1VoloAbpUsersUserData_VoloAbpUsersAbstractions_Version_9110_Culture_neutral_PublicKeyToken_null = {
    items?: Array<VoloAbpUsersUserData> | null;
};

export type VoloAbpApplicationDtosPagedResultDto1ImipEkbEmployeesEmployeeDto_ImipEkbApplicationContracts_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<ImipEkbEmployeesEmployeeDto> | null;
    totalCount?: number;
};

export type VoloAbpApplicationDtosPagedResultDto1ImipEkbProductsProductDto_ImipEkbApplicationContracts_Version_1000_Culture_neutral_PublicKeyToken_null = {
    items?: Array<ImipEkbProductsProductDto> | null;
    totalCount?: number;
};

export type VoloAbpApplicationDtosPagedResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null = {
    items?: Array<VoloAbpIdentityIdentityRoleDto> | null;
    totalCount?: number;
};

export type VoloAbpApplicationDtosPagedResultDto1VoloAbpIdentityIdentityUserDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null = {
    items?: Array<VoloAbpIdentityIdentityUserDto> | null;
    totalCount?: number;
};

export type VoloAbpApplicationDtosPagedResultDto1VoloAbpTenantManagementTenantDto_VoloAbpTenantManagementApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null = {
    items?: Array<VoloAbpTenantManagementTenantDto> | null;
    totalCount?: number;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationAuthConfigurationDto = {
    grantedPolicies?: {
        [key: string]: boolean;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationConfigurationDto = {
    localization?: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationConfigurationDto;
    auth?: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationAuthConfigurationDto;
    setting?: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationSettingConfigurationDto;
    currentUser?: VoloAbpAspNetCoreMvcApplicationConfigurationsCurrentUserDto;
    features?: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationFeatureConfigurationDto;
    globalFeatures?: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationGlobalFeatureConfigurationDto;
    multiTenancy?: VoloAbpAspNetCoreMvcMultiTenancyMultiTenancyInfoDto;
    currentTenant?: VoloAbpAspNetCoreMvcMultiTenancyCurrentTenantDto;
    timing?: VoloAbpAspNetCoreMvcApplicationConfigurationsTimingDto;
    clock?: VoloAbpAspNetCoreMvcApplicationConfigurationsClockDto;
    objectExtensions?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingObjectExtensionsDto;
    extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationFeatureConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationGlobalFeatureConfigurationDto = {
    enabledFeatures?: Array<string> | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationConfigurationDto = {
    values?: {
        [key: string]: {
            [key: string]: string;
        };
    } | null;
    resources?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationResourceDto;
    } | null;
    languages?: Array<VoloAbpLocalizationLanguageInfo> | null;
    currentCulture?: VoloAbpAspNetCoreMvcApplicationConfigurationsCurrentCultureDto;
    defaultResourceName?: string | null;
    languagesMap?: {
        [key: string]: Array<VoloAbpNameValue>;
    } | null;
    languageFilesMap?: {
        [key: string]: Array<VoloAbpNameValue>;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationDto = {
    resources?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationResourceDto;
    } | null;
    currentCulture?: VoloAbpAspNetCoreMvcApplicationConfigurationsCurrentCultureDto;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationResourceDto = {
    texts?: {
        [key: string]: string;
    } | null;
    baseResources?: Array<string> | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationSettingConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsClockDto = {
    kind?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsCurrentCultureDto = {
    displayName?: string | null;
    englishName?: string | null;
    threeLetterIsoLanguageName?: string | null;
    twoLetterIsoLanguageName?: string | null;
    isRightToLeft?: boolean;
    cultureName?: string | null;
    name?: string | null;
    nativeName?: string | null;
    dateTimeFormat?: VoloAbpAspNetCoreMvcApplicationConfigurationsDateTimeFormatDto;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsCurrentUserDto = {
    isAuthenticated?: boolean;
    id?: string | null;
    tenantId?: string | null;
    impersonatorUserId?: string | null;
    impersonatorTenantId?: string | null;
    impersonatorUserName?: string | null;
    impersonatorTenantName?: string | null;
    userName?: string | null;
    name?: string | null;
    surName?: string | null;
    email?: string | null;
    emailVerified?: boolean;
    phoneNumber?: string | null;
    phoneNumberVerified?: boolean;
    roles?: Array<string> | null;
    sessionId?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsDateTimeFormatDto = {
    calendarAlgorithmType?: string | null;
    dateTimeFormatLong?: string | null;
    shortDatePattern?: string | null;
    fullDateTimePattern?: string | null;
    dateSeparator?: string | null;
    shortTimePattern?: string | null;
    longTimePattern?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsIanaTimeZone = {
    timeZoneName?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingEntityExtensionDto = {
    properties?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionEnumDto = {
    fields?: Array<VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionEnumFieldDto> | null;
    localizationResource?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionEnumFieldDto = {
    name?: string | null;
    value?: unknown;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiCreateDto = {
    isAvailable?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiDto = {
    onGet?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiGetDto;
    onCreate?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiCreateDto;
    onUpdate?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiUpdateDto;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiGetDto = {
    isAvailable?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiUpdateDto = {
    isAvailable?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyAttributeDto = {
    typeSimple?: string | null;
    config?: {
        [key: string]: unknown;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyDto = {
    type?: string | null;
    typeSimple?: string | null;
    displayName?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingLocalizableStringDto;
    api?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyApiDto;
    ui?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiDto;
    policy?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyPolicyDto;
    attributes?: Array<VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyAttributeDto> | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
    defaultValue?: unknown;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyGlobalFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyPermissionPolicyDto = {
    permissionNames?: Array<string> | null;
    requiresAll?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyPolicyDto = {
    globalFeatures?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyGlobalFeaturePolicyDto;
    features?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyFeaturePolicyDto;
    permissions?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyPermissionPolicyDto;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiDto = {
    onTable?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiTableDto;
    onCreateForm?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiFormDto;
    onEditForm?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiFormDto;
    lookup?: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiLookupDto;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiFormDto = {
    isVisible?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiLookupDto = {
    url?: string | null;
    resultListPropertyName?: string | null;
    displayPropertyName?: string | null;
    valuePropertyName?: string | null;
    filterParamName?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionPropertyUiTableDto = {
    isVisible?: boolean;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingLocalizableStringDto = {
    name?: string | null;
    resource?: string | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingModuleExtensionDto = {
    entities?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingEntityExtensionDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingObjectExtensionsDto = {
    modules?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingModuleExtensionDto;
    } | null;
    enums?: {
        [key: string]: VoloAbpAspNetCoreMvcApplicationConfigurationsObjectExtendingExtensionEnumDto;
    } | null;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsTimeZone = {
    iana?: VoloAbpAspNetCoreMvcApplicationConfigurationsIanaTimeZone;
    windows?: VoloAbpAspNetCoreMvcApplicationConfigurationsWindowsTimeZone;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsTimingDto = {
    timeZone?: VoloAbpAspNetCoreMvcApplicationConfigurationsTimeZone;
};

export type VoloAbpAspNetCoreMvcApplicationConfigurationsWindowsTimeZone = {
    timeZoneId?: string | null;
};

export type VoloAbpAspNetCoreMvcMultiTenancyCurrentTenantDto = {
    id?: string | null;
    name?: string | null;
    isAvailable?: boolean;
};

export type VoloAbpAspNetCoreMvcMultiTenancyFindTenantResultDto = {
    success?: boolean;
    tenantId?: string | null;
    name?: string | null;
    normalizedName?: string | null;
    isActive?: boolean;
};

export type VoloAbpAspNetCoreMvcMultiTenancyMultiTenancyInfoDto = {
    isEnabled?: boolean;
};

export type VoloAbpFeatureManagementFeatureDtoReadable = {
    name?: string | null;
    displayName?: string | null;
    value?: string | null;
    provider?: VoloAbpFeatureManagementFeatureProviderDto;
    description?: string | null;
    valueType?: VoloAbpValidationStringValuesIStringValueType;
    depth?: number;
    parentName?: string | null;
};

export type VoloAbpFeatureManagementFeatureDtoWritable = {
    name?: string | null;
    displayName?: string | null;
    value?: string | null;
    provider?: VoloAbpFeatureManagementFeatureProviderDto;
    description?: string | null;
    depth?: number;
    parentName?: string | null;
};

export type VoloAbpFeatureManagementFeatureGroupDto = {
    name?: string | null;
    displayName?: string | null;
    features?: Array<VoloAbpFeatureManagementFeatureDto> | null;
};

export type VoloAbpFeatureManagementFeatureProviderDto = {
    name?: string | null;
    key?: string | null;
};

export type VoloAbpFeatureManagementGetFeatureListResultDto = {
    groups?: Array<VoloAbpFeatureManagementFeatureGroupDto> | null;
};

export type VoloAbpFeatureManagementUpdateFeatureDto = {
    name?: string | null;
    value?: string | null;
};

export type VoloAbpFeatureManagementUpdateFeaturesDto = {
    features?: Array<VoloAbpFeatureManagementUpdateFeatureDto> | null;
};

export type VoloAbpHttpModelingActionApiDescriptionModel = {
    uniqueName?: string | null;
    name?: string | null;
    httpMethod?: string | null;
    url?: string | null;
    supportedVersions?: Array<string> | null;
    parametersOnMethod?: Array<VoloAbpHttpModelingMethodParameterApiDescriptionModel> | null;
    parameters?: Array<VoloAbpHttpModelingParameterApiDescriptionModel> | null;
    returnValue?: VoloAbpHttpModelingReturnValueApiDescriptionModel;
    allowAnonymous?: boolean | null;
    implementFrom?: string | null;
};

export type VoloAbpHttpModelingApplicationApiDescriptionModel = {
    modules?: {
        [key: string]: VoloAbpHttpModelingModuleApiDescriptionModel;
    } | null;
    types?: {
        [key: string]: VoloAbpHttpModelingTypeApiDescriptionModel;
    } | null;
};

export type VoloAbpHttpModelingControllerApiDescriptionModel = {
    controllerName?: string | null;
    controllerGroupName?: string | null;
    isRemoteService?: boolean;
    isIntegrationService?: boolean;
    apiVersion?: string | null;
    type?: string | null;
    interfaces?: Array<VoloAbpHttpModelingControllerInterfaceApiDescriptionModel> | null;
    actions?: {
        [key: string]: VoloAbpHttpModelingActionApiDescriptionModel;
    } | null;
};

export type VoloAbpHttpModelingControllerInterfaceApiDescriptionModel = {
    type?: string | null;
    name?: string | null;
    methods?: Array<VoloAbpHttpModelingInterfaceMethodApiDescriptionModel> | null;
};

export type VoloAbpHttpModelingInterfaceMethodApiDescriptionModel = {
    name?: string | null;
    parametersOnMethod?: Array<VoloAbpHttpModelingMethodParameterApiDescriptionModel> | null;
    returnValue?: VoloAbpHttpModelingReturnValueApiDescriptionModel;
};

export type VoloAbpHttpModelingMethodParameterApiDescriptionModel = {
    name?: string | null;
    typeAsString?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isOptional?: boolean;
    defaultValue?: unknown;
};

export type VoloAbpHttpModelingModuleApiDescriptionModel = {
    rootPath?: string | null;
    remoteServiceName?: string | null;
    controllers?: {
        [key: string]: VoloAbpHttpModelingControllerApiDescriptionModel;
    } | null;
};

export type VoloAbpHttpModelingParameterApiDescriptionModel = {
    nameOnMethod?: string | null;
    name?: string | null;
    jsonName?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isOptional?: boolean;
    defaultValue?: unknown;
    constraintTypes?: Array<string> | null;
    bindingSourceId?: string | null;
    descriptorName?: string | null;
};

export type VoloAbpHttpModelingPropertyApiDescriptionModel = {
    name?: string | null;
    jsonName?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isRequired?: boolean;
    minLength?: number | null;
    maxLength?: number | null;
    minimum?: string | null;
    maximum?: string | null;
    regex?: string | null;
};

export type VoloAbpHttpModelingReturnValueApiDescriptionModel = {
    type?: string | null;
    typeSimple?: string | null;
};

export type VoloAbpHttpModelingTypeApiDescriptionModel = {
    baseType?: string | null;
    isEnum?: boolean;
    enumNames?: Array<string> | null;
    enumValues?: Array<unknown> | null;
    genericArguments?: Array<string> | null;
    properties?: Array<VoloAbpHttpModelingPropertyApiDescriptionModel> | null;
};

export type VoloAbpHttpRemoteServiceErrorInfo = {
    code?: string | null;
    message?: string | null;
    details?: string | null;
    data?: {
        [key: string]: unknown;
    } | null;
    validationErrors?: Array<VoloAbpHttpRemoteServiceValidationErrorInfo> | null;
};

export type VoloAbpHttpRemoteServiceErrorResponse = {
    error?: VoloAbpHttpRemoteServiceErrorInfo;
};

export type VoloAbpHttpRemoteServiceValidationErrorInfo = {
    message?: string | null;
    members?: Array<string> | null;
};

export type VoloAbpIdentityIdentityRoleCreateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
};

export type VoloAbpIdentityIdentityRoleCreateDtoWritable = {
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
};

export type VoloAbpIdentityIdentityRoleDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    isDefault?: boolean;
    isStatic?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
    creationTime?: string;
};

export type VoloAbpIdentityIdentityRoleDtoWritable = {
    id?: string;
    name?: string | null;
    isDefault?: boolean;
    isStatic?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
    creationTime?: string;
};

export type VoloAbpIdentityIdentityRoleUpdateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
};

export type VoloAbpIdentityIdentityRoleUpdateDtoWritable = {
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
};

export type VoloAbpIdentityIdentityUserCreateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password: string;
};

export type VoloAbpIdentityIdentityUserCreateDtoWritable = {
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password: string;
};

export type VoloAbpIdentityIdentityUserDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type VoloAbpIdentityIdentityUserDtoWritable = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type VoloAbpIdentityIdentityUserUpdateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpIdentityIdentityUserUpdateDtoWritable = {
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpIdentityIdentityUserUpdateRolesDto = {
    roleNames: Array<string>;
};

export type VoloAbpLocalizationLanguageInfoReadable = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
    readonly twoLetterISOLanguageName?: string | null;
};

export type VoloAbpLocalizationLanguageInfoWritable = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
};

export type VoloAbpNameValue = {
    name?: string | null;
    value?: string | null;
};

export type VoloAbpPermissionManagementGetPermissionListResultDto = {
    entityDisplayName?: string | null;
    groups?: Array<VoloAbpPermissionManagementPermissionGroupDto> | null;
};

export type VoloAbpPermissionManagementPermissionGrantInfoDto = {
    name?: string | null;
    displayName?: string | null;
    parentName?: string | null;
    isGranted?: boolean;
    allowedProviders?: Array<string> | null;
    grantedProviders?: Array<VoloAbpPermissionManagementProviderInfoDto> | null;
};

export type VoloAbpPermissionManagementPermissionGroupDto = {
    name?: string | null;
    displayName?: string | null;
    displayNameKey?: string | null;
    displayNameResource?: string | null;
    permissions?: Array<VoloAbpPermissionManagementPermissionGrantInfoDto> | null;
};

export type VoloAbpPermissionManagementProviderInfoDto = {
    providerName?: string | null;
    providerKey?: string | null;
};

export type VoloAbpPermissionManagementUpdatePermissionDto = {
    name?: string | null;
    isGranted?: boolean;
};

export type VoloAbpPermissionManagementUpdatePermissionsDto = {
    permissions?: Array<VoloAbpPermissionManagementUpdatePermissionDto> | null;
};

export type VoloAbpSettingManagementEmailSettingsDto = {
    smtpHost?: string | null;
    smtpPort?: number;
    smtpUserName?: string | null;
    smtpPassword?: string | null;
    smtpDomain?: string | null;
    smtpEnableSsl?: boolean;
    smtpUseDefaultCredentials?: boolean;
    defaultFromAddress?: string | null;
    defaultFromDisplayName?: string | null;
};

export type VoloAbpSettingManagementSendTestEmailInput = {
    senderEmailAddress: string;
    targetEmailAddress: string;
    subject: string;
    body?: string | null;
};

export type VoloAbpSettingManagementUpdateEmailSettingsDto = {
    smtpHost?: string | null;
    smtpPort?: number;
    smtpUserName?: string | null;
    smtpPassword?: string | null;
    smtpDomain?: string | null;
    smtpEnableSsl?: boolean;
    smtpUseDefaultCredentials?: boolean;
    defaultFromAddress: string;
    defaultFromDisplayName: string;
};

export type VoloAbpTenantManagementTenantCreateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type VoloAbpTenantManagementTenantCreateDtoWritable = {
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type VoloAbpTenantManagementTenantDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpTenantManagementTenantDtoWritable = {
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type VoloAbpTenantManagementTenantUpdateDtoReadable = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    concurrencyStamp?: string | null;
};

export type VoloAbpTenantManagementTenantUpdateDtoWritable = {
    name: string;
    concurrencyStamp?: string | null;
};

export type VoloAbpUsersUserDataReadable = {
    id?: string;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    isActive?: boolean;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type VoloAbpUsersUserDataWritable = {
    id?: string;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    isActive?: boolean;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
};

export type VoloAbpValidationStringValuesIStringValueType = {
    readonly name?: string | null;
    readonly properties?: {
        [key: string]: unknown;
    } | null;
    validator?: VoloAbpValidationStringValuesIValueValidator;
};

export type VoloAbpValidationStringValuesIValueValidator = {
    readonly name?: string | null;
    readonly properties?: {
        [key: string]: unknown;
    } | null;
};

export type GetApiAbpApiDefinitionData = {
    body?: never;
    path?: never;
    query?: {
        IncludeTypes?: boolean;
    };
    url: '/api/abp/api-definition';
};

export type GetApiAbpApiDefinitionErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAbpApiDefinitionError = GetApiAbpApiDefinitionErrors[keyof GetApiAbpApiDefinitionErrors];

export type GetApiAbpApiDefinitionResponses = {
    /**
     * OK
     */
    200: VoloAbpHttpModelingApplicationApiDescriptionModel;
};

export type GetApiAbpApiDefinitionResponse = GetApiAbpApiDefinitionResponses[keyof GetApiAbpApiDefinitionResponses];

export type GetApiAbpApplicationConfigurationData = {
    body?: never;
    path?: never;
    query?: {
        IncludeLocalizationResources?: boolean;
    };
    url: '/api/abp/application-configuration';
};

export type GetApiAbpApplicationConfigurationErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError = GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
    /**
     * OK
     */
    200: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse = GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpApplicationLocalizationData = {
    body?: never;
    path?: never;
    query: {
        CultureName: string;
        OnlyDynamics?: boolean;
    };
    url: '/api/abp/application-localization';
};

export type GetApiAbpApplicationLocalizationErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAbpApplicationLocalizationError = GetApiAbpApplicationLocalizationErrors[keyof GetApiAbpApplicationLocalizationErrors];

export type GetApiAbpApplicationLocalizationResponses = {
    /**
     * OK
     */
    200: VoloAbpAspNetCoreMvcApplicationConfigurationsApplicationLocalizationDto;
};

export type GetApiAbpApplicationLocalizationResponse = GetApiAbpApplicationLocalizationResponses[keyof GetApiAbpApplicationLocalizationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-name/{name}';
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError = GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
    /**
     * OK
     */
    200: VoloAbpAspNetCoreMvcMultiTenancyFindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse = GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-id/{id}';
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError = GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpAspNetCoreMvcMultiTenancyFindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse = GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type PostApiAccountRegisterData = {
    body?: VoloAbpAccountRegisterDtoWritable;
    path?: never;
    query?: never;
    url: '/api/account/register';
};

export type PostApiAccountRegisterErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError = PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type PostApiAccountRegisterResponse = PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
    body?: VoloAbpAccountSendPasswordResetCodeDto;
    path?: never;
    query?: never;
    url: '/api/account/send-password-reset-code';
};

export type PostApiAccountSendPasswordResetCodeErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError = PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
    body?: VoloAbpAccountVerifyPasswordResetTokenInput;
    path?: never;
    query?: never;
    url: '/api/account/verify-password-reset-token';
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError = PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse = PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
    body?: VoloAbpAccountResetPasswordDto;
    path?: never;
    query?: never;
    url: '/api/account/reset-password';
};

export type PostApiAccountResetPasswordErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError = PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountDynamicClaimsRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/dynamic-claims/refresh';
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError = PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementEmailingData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing';
};

export type GetApiSettingManagementEmailingErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiSettingManagementEmailingError = GetApiSettingManagementEmailingErrors[keyof GetApiSettingManagementEmailingErrors];

export type GetApiSettingManagementEmailingResponses = {
    /**
     * OK
     */
    200: VoloAbpSettingManagementEmailSettingsDto;
};

export type GetApiSettingManagementEmailingResponse = GetApiSettingManagementEmailingResponses[keyof GetApiSettingManagementEmailingResponses];

export type PostApiSettingManagementEmailingData = {
    body?: VoloAbpSettingManagementUpdateEmailSettingsDto;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing';
};

export type PostApiSettingManagementEmailingErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiSettingManagementEmailingError = PostApiSettingManagementEmailingErrors[keyof PostApiSettingManagementEmailingErrors];

export type PostApiSettingManagementEmailingResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiSettingManagementEmailingSendTestEmailData = {
    body?: VoloAbpSettingManagementSendTestEmailInput;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing/send-test-email';
};

export type PostApiSettingManagementEmailingSendTestEmailErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiSettingManagementEmailingSendTestEmailError = PostApiSettingManagementEmailingSendTestEmailErrors[keyof PostApiSettingManagementEmailingSendTestEmailErrors];

export type PostApiSettingManagementEmailingSendTestEmailResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAppEmployeeData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Department?: string;
        IsActive?: boolean;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/app/employee';
};

export type GetApiAppEmployeeErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAppEmployeeError = GetApiAppEmployeeErrors[keyof GetApiAppEmployeeErrors];

export type GetApiAppEmployeeResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1ImipEkbEmployeesEmployeeDto_ImipEkbApplicationContracts_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiAppEmployeeResponse = GetApiAppEmployeeResponses[keyof GetApiAppEmployeeResponses];

export type PostApiAppEmployeeData = {
    body?: ImipEkbEmployeesCreateUpdateEmployeeDto;
    path?: never;
    query?: never;
    url: '/api/app/employee';
};

export type PostApiAppEmployeeErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppEmployeeError = PostApiAppEmployeeErrors[keyof PostApiAppEmployeeErrors];

export type PostApiAppEmployeeResponses = {
    /**
     * OK
     */
    200: ImipEkbEmployeesEmployeeDto;
};

export type PostApiAppEmployeeResponse = PostApiAppEmployeeResponses[keyof PostApiAppEmployeeResponses];

export type DeleteApiAppEmployeeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/employee/{id}';
};

export type DeleteApiAppEmployeeByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiAppEmployeeByIdError = DeleteApiAppEmployeeByIdErrors[keyof DeleteApiAppEmployeeByIdErrors];

export type DeleteApiAppEmployeeByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAppEmployeeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/employee/{id}';
};

export type GetApiAppEmployeeByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAppEmployeeByIdError = GetApiAppEmployeeByIdErrors[keyof GetApiAppEmployeeByIdErrors];

export type GetApiAppEmployeeByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbEmployeesEmployeeDto;
};

export type GetApiAppEmployeeByIdResponse = GetApiAppEmployeeByIdResponses[keyof GetApiAppEmployeeByIdResponses];

export type PutApiAppEmployeeByIdData = {
    body?: ImipEkbEmployeesCreateUpdateEmployeeDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/employee/{id}';
};

export type PutApiAppEmployeeByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiAppEmployeeByIdError = PutApiAppEmployeeByIdErrors[keyof PutApiAppEmployeeByIdErrors];

export type PutApiAppEmployeeByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbEmployeesEmployeeDto;
};

export type PutApiAppEmployeeByIdResponse = PutApiAppEmployeeByIdResponses[keyof PutApiAppEmployeeByIdResponses];

export type GetApiAppEmployeeDepartmentsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/app/employee/departments';
};

export type GetApiAppEmployeeDepartmentsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAppEmployeeDepartmentsError = GetApiAppEmployeeDepartmentsErrors[keyof GetApiAppEmployeeDepartmentsErrors];

export type GetApiAppEmployeeDepartmentsResponses = {
    /**
     * OK
     */
    200: Array<string>;
};

export type GetApiAppEmployeeDepartmentsResponse = GetApiAppEmployeeDepartmentsResponses[keyof GetApiAppEmployeeDepartmentsResponses];

export type PostApiAppEmployeeIsEmailUniqueData = {
    body?: never;
    path?: never;
    query?: {
        email?: string;
        excludeId?: string;
    };
    url: '/api/app/employee/is-email-unique';
};

export type PostApiAppEmployeeIsEmailUniqueErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppEmployeeIsEmailUniqueError = PostApiAppEmployeeIsEmailUniqueErrors[keyof PostApiAppEmployeeIsEmailUniqueErrors];

export type PostApiAppEmployeeIsEmailUniqueResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAppEmployeeIsEmailUniqueResponse = PostApiAppEmployeeIsEmailUniqueResponses[keyof PostApiAppEmployeeIsEmailUniqueResponses];

export type DeleteApiAppEmployeeMultipleData = {
    body?: never;
    path?: never;
    query?: {
        ids?: Array<string>;
    };
    url: '/api/app/employee/multiple';
};

export type DeleteApiAppEmployeeMultipleErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiAppEmployeeMultipleError = DeleteApiAppEmployeeMultipleErrors[keyof DeleteApiAppEmployeeMultipleErrors];

export type DeleteApiAppEmployeeMultipleResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAppEmployeeExportToExcelData = {
    body?: ImipEkbEmployeesGetEmployeeListDto;
    path?: never;
    query?: never;
    url: '/api/app/employee/export-to-excel';
};

export type PostApiAppEmployeeExportToExcelErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppEmployeeExportToExcelError = PostApiAppEmployeeExportToExcelErrors[keyof PostApiAppEmployeeExportToExcelErrors];

export type PostApiAppEmployeeExportToExcelResponses = {
    /**
     * OK
     */
    200: string;
};

export type PostApiAppEmployeeExportToExcelResponse = PostApiAppEmployeeExportToExcelResponses[keyof PostApiAppEmployeeExportToExcelResponses];

export type PostApiAppEmployeeByIdActivateData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/employee/{id}/activate';
};

export type PostApiAppEmployeeByIdActivateErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppEmployeeByIdActivateError = PostApiAppEmployeeByIdActivateErrors[keyof PostApiAppEmployeeByIdActivateErrors];

export type PostApiAppEmployeeByIdActivateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAppEmployeeByIdDeactivateData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/employee/{id}/deactivate';
};

export type PostApiAppEmployeeByIdDeactivateErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppEmployeeByIdDeactivateError = PostApiAppEmployeeByIdDeactivateErrors[keyof PostApiAppEmployeeByIdDeactivateErrors];

export type PostApiAppEmployeeByIdDeactivateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiFeatureManagementFeaturesData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type DeleteApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiFeatureManagementFeaturesError = DeleteApiFeatureManagementFeaturesErrors[keyof DeleteApiFeatureManagementFeaturesErrors];

export type DeleteApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiFeatureManagementFeaturesData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type GetApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiFeatureManagementFeaturesError = GetApiFeatureManagementFeaturesErrors[keyof GetApiFeatureManagementFeaturesErrors];

export type GetApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: VoloAbpFeatureManagementGetFeatureListResultDto;
};

export type GetApiFeatureManagementFeaturesResponse = GetApiFeatureManagementFeaturesResponses[keyof GetApiFeatureManagementFeaturesResponses];

export type PutApiFeatureManagementFeaturesData = {
    body?: VoloAbpFeatureManagementUpdateFeaturesDto;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type PutApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiFeatureManagementFeaturesError = PutApiFeatureManagementFeaturesErrors[keyof PutApiFeatureManagementFeaturesErrors];

export type PutApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountLoginData = {
    body?: VoloAbpAccountWebAreasAccountControllersModelsUserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/login';
};

export type PostApiAccountLoginErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountLoginError = PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
    /**
     * OK
     */
    200: VoloAbpAccountWebAreasAccountControllersModelsAbpLoginResult;
};

export type PostApiAccountLoginResponse = PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/logout';
};

export type GetApiAccountLogoutErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError = GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountCheckPasswordData = {
    body?: VoloAbpAccountWebAreasAccountControllersModelsUserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/check-password';
};

export type PostApiAccountCheckPasswordErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError = PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
    /**
     * OK
     */
    200: VoloAbpAccountWebAreasAccountControllersModelsAbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse = PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type GetApiPermissionManagementPermissionsData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/permission-management/permissions';
};

export type GetApiPermissionManagementPermissionsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiPermissionManagementPermissionsError = GetApiPermissionManagementPermissionsErrors[keyof GetApiPermissionManagementPermissionsErrors];

export type GetApiPermissionManagementPermissionsResponses = {
    /**
     * OK
     */
    200: VoloAbpPermissionManagementGetPermissionListResultDto;
};

export type GetApiPermissionManagementPermissionsResponse = GetApiPermissionManagementPermissionsResponses[keyof GetApiPermissionManagementPermissionsResponses];

export type PutApiPermissionManagementPermissionsData = {
    body?: VoloAbpPermissionManagementUpdatePermissionsDto;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/permission-management/permissions';
};

export type PutApiPermissionManagementPermissionsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiPermissionManagementPermissionsError = PutApiPermissionManagementPermissionsErrors[keyof PutApiPermissionManagementPermissionsErrors];

export type PutApiPermissionManagementPermissionsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAppProductData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        IsActive?: boolean;
        MinPrice?: number;
        MaxPrice?: number;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/app/product';
};

export type GetApiAppProductErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAppProductError = GetApiAppProductErrors[keyof GetApiAppProductErrors];

export type GetApiAppProductResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1ImipEkbProductsProductDto_ImipEkbApplicationContracts_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiAppProductResponse = GetApiAppProductResponses[keyof GetApiAppProductResponses];

export type PostApiAppProductData = {
    body?: ImipEkbProductsCreateUpdateProductDto;
    path?: never;
    query?: never;
    url: '/api/app/product';
};

export type PostApiAppProductErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppProductError = PostApiAppProductErrors[keyof PostApiAppProductErrors];

export type PostApiAppProductResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PostApiAppProductResponse = PostApiAppProductResponses[keyof PostApiAppProductResponses];

export type DeleteApiAppProductByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/product/{id}';
};

export type DeleteApiAppProductByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiAppProductByIdError = DeleteApiAppProductByIdErrors[keyof DeleteApiAppProductByIdErrors];

export type DeleteApiAppProductByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAppProductByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/product/{id}';
};

export type GetApiAppProductByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAppProductByIdError = GetApiAppProductByIdErrors[keyof GetApiAppProductByIdErrors];

export type GetApiAppProductByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type GetApiAppProductByIdResponse = GetApiAppProductByIdResponses[keyof GetApiAppProductByIdResponses];

export type PutApiAppProductByIdData = {
    body?: ImipEkbProductsCreateUpdateProductDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/app/product/{id}';
};

export type PutApiAppProductByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiAppProductByIdError = PutApiAppProductByIdErrors[keyof PutApiAppProductByIdErrors];

export type PutApiAppProductByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PutApiAppProductByIdResponse = PutApiAppProductByIdResponses[keyof PutApiAppProductByIdResponses];

export type PostApiAppProductIsSkuInUseData = {
    body?: never;
    path?: never;
    query?: {
        sku?: string;
    };
    url: '/api/app/product/is-sku-in-use';
};

export type PostApiAppProductIsSkuInUseErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAppProductIsSkuInUseError = PostApiAppProductIsSkuInUseErrors[keyof PostApiAppProductIsSkuInUseErrors];

export type PostApiAppProductIsSkuInUseResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAppProductIsSkuInUseResponse = PostApiAppProductIsSkuInUseResponses[keyof PostApiAppProductIsSkuInUseResponses];

export type PutApiAppProductByIdStockQuantityData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        newQuantity?: number;
    };
    url: '/api/app/product/{id}/stock-quantity';
};

export type PutApiAppProductByIdStockQuantityErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiAppProductByIdStockQuantityError = PutApiAppProductByIdStockQuantityErrors[keyof PutApiAppProductByIdStockQuantityErrors];

export type PutApiAppProductByIdStockQuantityResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PutApiAppProductByIdStockQuantityResponse = PutApiAppProductByIdStockQuantityResponses[keyof PutApiAppProductByIdStockQuantityResponses];

export type GetApiProductsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        IsActive?: boolean;
        MinPrice?: number;
        MaxPrice?: number;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/products';
};

export type GetApiProductsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiProductsError = GetApiProductsErrors[keyof GetApiProductsErrors];

export type GetApiProductsResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1ImipEkbProductsProductDto_ImipEkbApplicationContracts_Version_1000_Culture_neutral_PublicKeyToken_null;
};

export type GetApiProductsResponse = GetApiProductsResponses[keyof GetApiProductsResponses];

export type PostApiProductsData = {
    body?: ImipEkbProductsCreateUpdateProductDto;
    path?: never;
    query?: never;
    url: '/api/products';
};

export type PostApiProductsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiProductsError = PostApiProductsErrors[keyof PostApiProductsErrors];

export type PostApiProductsResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PostApiProductsResponse = PostApiProductsResponses[keyof PostApiProductsResponses];

export type DeleteApiProductsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/products/{id}';
};

export type DeleteApiProductsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiProductsByIdError = DeleteApiProductsByIdErrors[keyof DeleteApiProductsByIdErrors];

export type DeleteApiProductsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiProductsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/products/{id}';
};

export type GetApiProductsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiProductsByIdError = GetApiProductsByIdErrors[keyof GetApiProductsByIdErrors];

export type GetApiProductsByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type GetApiProductsByIdResponse = GetApiProductsByIdResponses[keyof GetApiProductsByIdResponses];

export type PutApiProductsByIdData = {
    body?: ImipEkbProductsCreateUpdateProductDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/products/{id}';
};

export type PutApiProductsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiProductsByIdError = PutApiProductsByIdErrors[keyof PutApiProductsByIdErrors];

export type PutApiProductsByIdResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PutApiProductsByIdResponse = PutApiProductsByIdResponses[keyof PutApiProductsByIdResponses];

export type GetApiProductsCheckSkuBySkuData = {
    body?: never;
    path: {
        sku: string;
    };
    query?: never;
    url: '/api/products/check-sku/{sku}';
};

export type GetApiProductsCheckSkuBySkuErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiProductsCheckSkuBySkuError = GetApiProductsCheckSkuBySkuErrors[keyof GetApiProductsCheckSkuBySkuErrors];

export type GetApiProductsCheckSkuBySkuResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type GetApiProductsCheckSkuBySkuResponse = GetApiProductsCheckSkuBySkuResponses[keyof GetApiProductsCheckSkuBySkuResponses];

export type PutApiProductsByIdStockData = {
    body?: number;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/products/{id}/stock';
};

export type PutApiProductsByIdStockErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiProductsByIdStockError = PutApiProductsByIdStockErrors[keyof PutApiProductsByIdStockErrors];

export type PutApiProductsByIdStockResponses = {
    /**
     * OK
     */
    200: ImipEkbProductsProductDto;
};

export type PutApiProductsByIdStockResponse = PutApiProductsByIdStockResponses[keyof PutApiProductsByIdStockResponses];

export type GetApiAccountMyProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type GetApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError = GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: VoloAbpAccountProfileDtoReadable;
};

export type GetApiAccountMyProfileResponse = GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
    body?: VoloAbpAccountUpdateProfileDtoWritable;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type PutApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError = PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: VoloAbpAccountProfileDtoReadable;
};

export type PutApiAccountMyProfileResponse = PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
    body?: VoloAbpAccountChangePasswordInput;
    path?: never;
    query?: never;
    url: '/api/account/my-profile/change-password';
};

export type PostApiAccountMyProfileChangePasswordErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError = PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityRolesAllData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/identity/roles/all';
};

export type GetApiIdentityRolesAllErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityRolesAllError = GetApiIdentityRolesAllErrors[keyof GetApiIdentityRolesAllErrors];

export type GetApiIdentityRolesAllResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosListResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityRolesAllResponse = GetApiIdentityRolesAllResponses[keyof GetApiIdentityRolesAllResponses];

export type GetApiIdentityRolesData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/roles';
};

export type GetApiIdentityRolesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityRolesError = GetApiIdentityRolesErrors[keyof GetApiIdentityRolesErrors];

export type GetApiIdentityRolesResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityRolesResponse = GetApiIdentityRolesResponses[keyof GetApiIdentityRolesResponses];

export type PostApiIdentityRolesData = {
    body?: VoloAbpIdentityIdentityRoleCreateDtoWritable;
    path?: never;
    query?: never;
    url: '/api/identity/roles';
};

export type PostApiIdentityRolesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiIdentityRolesError = PostApiIdentityRolesErrors[keyof PostApiIdentityRolesErrors];

export type PostApiIdentityRolesResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityRoleDtoReadable;
};

export type PostApiIdentityRolesResponse = PostApiIdentityRolesResponses[keyof PostApiIdentityRolesResponses];

export type DeleteApiIdentityRolesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type DeleteApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiIdentityRolesByIdError = DeleteApiIdentityRolesByIdErrors[keyof DeleteApiIdentityRolesByIdErrors];

export type DeleteApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityRolesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type GetApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityRolesByIdError = GetApiIdentityRolesByIdErrors[keyof GetApiIdentityRolesByIdErrors];

export type GetApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityRoleDtoReadable;
};

export type GetApiIdentityRolesByIdResponse = GetApiIdentityRolesByIdResponses[keyof GetApiIdentityRolesByIdResponses];

export type PutApiIdentityRolesByIdData = {
    body?: VoloAbpIdentityIdentityRoleUpdateDtoWritable;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type PutApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiIdentityRolesByIdError = PutApiIdentityRolesByIdErrors[keyof PutApiIdentityRolesByIdErrors];

export type PutApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityRoleDtoReadable;
};

export type PutApiIdentityRolesByIdResponse = PutApiIdentityRolesByIdResponses[keyof PutApiIdentityRolesByIdResponses];

export type DeleteApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError = DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type GetApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError = GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpTenantManagementTenantDtoReadable;
};

export type GetApiMultiTenancyTenantsByIdResponse = GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
    body?: VoloAbpTenantManagementTenantUpdateDtoWritable;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type PutApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError = PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpTenantManagementTenantDtoReadable;
};

export type PutApiMultiTenancyTenantsByIdResponse = PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/multi-tenancy/tenants';
};

export type GetApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError = GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1VoloAbpTenantManagementTenantDto_VoloAbpTenantManagementApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiMultiTenancyTenantsResponse = GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
    body?: VoloAbpTenantManagementTenantCreateDtoWritable;
    path?: never;
    query?: never;
    url: '/api/multi-tenancy/tenants';
};

export type PostApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError = PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: VoloAbpTenantManagementTenantDtoReadable;
};

export type PostApiMultiTenancyTenantsResponse = PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError = DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError = GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse = GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        defaultConnectionString?: string;
    };
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError = PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementTimezoneData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/timezone';
};

export type GetApiSettingManagementTimezoneErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiSettingManagementTimezoneError = GetApiSettingManagementTimezoneErrors[keyof GetApiSettingManagementTimezoneErrors];

export type GetApiSettingManagementTimezoneResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiSettingManagementTimezoneResponse = GetApiSettingManagementTimezoneResponses[keyof GetApiSettingManagementTimezoneResponses];

export type PostApiSettingManagementTimezoneData = {
    body?: never;
    path?: never;
    query?: {
        timezone?: string;
    };
    url: '/api/setting-management/timezone';
};

export type PostApiSettingManagementTimezoneErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiSettingManagementTimezoneError = PostApiSettingManagementTimezoneErrors[keyof PostApiSettingManagementTimezoneErrors];

export type PostApiSettingManagementTimezoneResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementTimezoneTimezonesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/timezone/timezones';
};

export type GetApiSettingManagementTimezoneTimezonesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiSettingManagementTimezoneTimezonesError = GetApiSettingManagementTimezoneTimezonesErrors[keyof GetApiSettingManagementTimezoneTimezonesErrors];

export type GetApiSettingManagementTimezoneTimezonesResponses = {
    /**
     * OK
     */
    200: Array<VoloAbpNameValue>;
};

export type GetApiSettingManagementTimezoneTimezonesResponse = GetApiSettingManagementTimezoneTimezonesResponses[keyof GetApiSettingManagementTimezoneTimezonesResponses];

export type DeleteApiIdentityUsersByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type DeleteApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type DeleteApiIdentityUsersByIdError = DeleteApiIdentityUsersByIdErrors[keyof DeleteApiIdentityUsersByIdErrors];

export type DeleteApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityUsersByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type GetApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByIdError = GetApiIdentityUsersByIdErrors[keyof GetApiIdentityUsersByIdErrors];

export type GetApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type GetApiIdentityUsersByIdResponse = GetApiIdentityUsersByIdResponses[keyof GetApiIdentityUsersByIdResponses];

export type PutApiIdentityUsersByIdData = {
    body?: VoloAbpIdentityIdentityUserUpdateDtoWritable;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type PutApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiIdentityUsersByIdError = PutApiIdentityUsersByIdErrors[keyof PutApiIdentityUsersByIdErrors];

export type PutApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type PutApiIdentityUsersByIdResponse = PutApiIdentityUsersByIdResponses[keyof PutApiIdentityUsersByIdResponses];

export type GetApiIdentityUsersData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/users';
};

export type GetApiIdentityUsersErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersError = GetApiIdentityUsersErrors[keyof GetApiIdentityUsersErrors];

export type GetApiIdentityUsersResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosPagedResultDto1VoloAbpIdentityIdentityUserDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityUsersResponse = GetApiIdentityUsersResponses[keyof GetApiIdentityUsersResponses];

export type PostApiIdentityUsersData = {
    body?: VoloAbpIdentityIdentityUserCreateDtoWritable;
    path?: never;
    query?: never;
    url: '/api/identity/users';
};

export type PostApiIdentityUsersErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PostApiIdentityUsersError = PostApiIdentityUsersErrors[keyof PostApiIdentityUsersErrors];

export type PostApiIdentityUsersResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type PostApiIdentityUsersResponse = PostApiIdentityUsersResponses[keyof PostApiIdentityUsersResponses];

export type GetApiIdentityUsersByIdRolesData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}/roles';
};

export type GetApiIdentityUsersByIdRolesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByIdRolesError = GetApiIdentityUsersByIdRolesErrors[keyof GetApiIdentityUsersByIdRolesErrors];

export type GetApiIdentityUsersByIdRolesResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosListResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityUsersByIdRolesResponse = GetApiIdentityUsersByIdRolesResponses[keyof GetApiIdentityUsersByIdRolesResponses];

export type PutApiIdentityUsersByIdRolesData = {
    body?: VoloAbpIdentityIdentityUserUpdateRolesDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}/roles';
};

export type PutApiIdentityUsersByIdRolesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type PutApiIdentityUsersByIdRolesError = PutApiIdentityUsersByIdRolesErrors[keyof PutApiIdentityUsersByIdRolesErrors];

export type PutApiIdentityUsersByIdRolesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityUsersAssignableRolesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/identity/users/assignable-roles';
};

export type GetApiIdentityUsersAssignableRolesErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersAssignableRolesError = GetApiIdentityUsersAssignableRolesErrors[keyof GetApiIdentityUsersAssignableRolesErrors];

export type GetApiIdentityUsersAssignableRolesResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosListResultDto1VoloAbpIdentityIdentityRoleDto_VoloAbpIdentityApplicationContracts_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityUsersAssignableRolesResponse = GetApiIdentityUsersAssignableRolesResponses[keyof GetApiIdentityUsersAssignableRolesResponses];

export type GetApiIdentityUsersByUsernameByUserNameData = {
    body?: never;
    path: {
        userName: string;
    };
    query?: never;
    url: '/api/identity/users/by-username/{userName}';
};

export type GetApiIdentityUsersByUsernameByUserNameErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByUsernameByUserNameError = GetApiIdentityUsersByUsernameByUserNameErrors[keyof GetApiIdentityUsersByUsernameByUserNameErrors];

export type GetApiIdentityUsersByUsernameByUserNameResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type GetApiIdentityUsersByUsernameByUserNameResponse = GetApiIdentityUsersByUsernameByUserNameResponses[keyof GetApiIdentityUsersByUsernameByUserNameResponses];

export type GetApiIdentityUsersByEmailByEmailData = {
    body?: never;
    path: {
        email: string;
    };
    query?: never;
    url: '/api/identity/users/by-email/{email}';
};

export type GetApiIdentityUsersByEmailByEmailErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByEmailByEmailError = GetApiIdentityUsersByEmailByEmailErrors[keyof GetApiIdentityUsersByEmailByEmailErrors];

export type GetApiIdentityUsersByEmailByEmailResponses = {
    /**
     * OK
     */
    200: VoloAbpIdentityIdentityUserDtoReadable;
};

export type GetApiIdentityUsersByEmailByEmailResponse = GetApiIdentityUsersByEmailByEmailResponses[keyof GetApiIdentityUsersByEmailByEmailResponses];

export type GetApiIdentityUsersLookupByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/lookup/{id}';
};

export type GetApiIdentityUsersLookupByIdErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupByIdError = GetApiIdentityUsersLookupByIdErrors[keyof GetApiIdentityUsersLookupByIdErrors];

export type GetApiIdentityUsersLookupByIdResponses = {
    /**
     * OK
     */
    200: VoloAbpUsersUserDataReadable;
};

export type GetApiIdentityUsersLookupByIdResponse = GetApiIdentityUsersLookupByIdResponses[keyof GetApiIdentityUsersLookupByIdResponses];

export type GetApiIdentityUsersLookupByUsernameByUserNameData = {
    body?: never;
    path: {
        userName: string;
    };
    query?: never;
    url: '/api/identity/users/lookup/by-username/{userName}';
};

export type GetApiIdentityUsersLookupByUsernameByUserNameErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupByUsernameByUserNameError = GetApiIdentityUsersLookupByUsernameByUserNameErrors[keyof GetApiIdentityUsersLookupByUsernameByUserNameErrors];

export type GetApiIdentityUsersLookupByUsernameByUserNameResponses = {
    /**
     * OK
     */
    200: VoloAbpUsersUserDataReadable;
};

export type GetApiIdentityUsersLookupByUsernameByUserNameResponse = GetApiIdentityUsersLookupByUsernameByUserNameResponses[keyof GetApiIdentityUsersLookupByUsernameByUserNameResponses];

export type GetApiIdentityUsersLookupSearchData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/users/lookup/search';
};

export type GetApiIdentityUsersLookupSearchErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupSearchError = GetApiIdentityUsersLookupSearchErrors[keyof GetApiIdentityUsersLookupSearchErrors];

export type GetApiIdentityUsersLookupSearchResponses = {
    /**
     * OK
     */
    200: VoloAbpApplicationDtosListResultDto1VoloAbpUsersUserData_VoloAbpUsersAbstractions_Version_9110_Culture_neutral_PublicKeyToken_null;
};

export type GetApiIdentityUsersLookupSearchResponse = GetApiIdentityUsersLookupSearchResponses[keyof GetApiIdentityUsersLookupSearchResponses];

export type GetApiIdentityUsersLookupCountData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
    };
    url: '/api/identity/users/lookup/count';
};

export type GetApiIdentityUsersLookupCountErrors = {
    /**
     * Bad Request
     */
    400: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: VoloAbpHttpRemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: VoloAbpHttpRemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupCountError = GetApiIdentityUsersLookupCountErrors[keyof GetApiIdentityUsersLookupCountErrors];

export type GetApiIdentityUsersLookupCountResponses = {
    /**
     * OK
     */
    200: number;
};

export type GetApiIdentityUsersLookupCountResponse = GetApiIdentityUsersLookupCountResponses[keyof GetApiIdentityUsersLookupCountResponses];

export type ClientOptions = {
    baseUrl: `${string}://swagger.json` | (string & {});
};