// Employee API service using direct API calls
import axios from 'axios';

export interface CreateUpdateEmployeeDto {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  department: string;
  position: string;
  hireDate: string;
  salary: number;
  photoPath?: string;
}

export interface EmployeeDto extends CreateUpdateEmployeeDto {
  id: string;
  isActive: boolean;
  creationTime: string;
}

export interface GetEmployeeListDto {
  skipCount?: number;
  maxResultCount?: number;
  sorting?: string;
  filter?: string;
  department?: string;
}

export interface PagedResultDto<T> {
  items: T[];
  totalCount: number;
}

class EmployeeApiService {
  private baseUrl = '/api/app/employee';

  async getList(input: GetEmployeeListDto): Promise<PagedResultDto<EmployeeDto>> {
    const response = await axios.get(this.baseUrl, { params: input });
    return response.data;
  }

  async get(id: string): Promise<EmployeeDto> {
    const response = await axios.get(`${this.baseUrl}/${id}`);
    return response.data;
  }

  async create(input: CreateUpdateEmployeeDto): Promise<EmployeeDto> {
    const response = await axios.post(this.baseUrl, input);
    return response.data;
  }

  async update(id: string, input: CreateUpdateEmployeeDto): Promise<EmployeeDto> {
    const response = await axios.put(`${this.baseUrl}/${id}`, input);
    return response.data;
  }

  async delete(id: string): Promise<void> {
    await axios.delete(`${this.baseUrl}/${id}`);
  }

  async getDepartments(): Promise<string[]> {
    const response = await axios.get(`${this.baseUrl}/departments`);
    return response.data;
  }

  async deleteMultiple(ids: string[]): Promise<void> {
    await axios.post(`${this.baseUrl}/delete-multiple`, ids);
  }
}

export const employeeApi = new EmployeeApiService();
