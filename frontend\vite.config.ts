import { mkdirSync } from "fs"
import path from "path"
import { defineConfig } from "vite"
import laravel from "laravel-vite-plugin"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"

const outDir = '../src/Imip.Ekb.Web/wwwroot/build'
mkdirSync(outDir, { recursive: true })

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    laravel({
      input: ['src/App.tsx'],
      publicDirectory: outDir,
      refresh: true,
    }),
    react(), 
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir,
    emptyOutDir: true,
  },
})