using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Surveyor.Dtos;

public class SurveyorDto : AuditedEntityDto<Guid>
{
    public long DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public string IsActive { get; set; } = null!;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}