using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Tenant.Dtos;

public class MasterTenantDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string CreatedBy { get; set; } = null!;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Flags { get; set; }
    public string? FullName { get; set; }
    public string? LetterPerson { get; set; }
    public string? LetterRole { get; set; }
    public string? Npwp { get; set; }
    public string? Address { get; set; }
    public string? Nib { get; set; }
    public string? Phone { get; set; }
    public string Status { get; set; } = null!;
    public string? NoAndDateNotaris { get; set; }
    public string? DescNotaris { get; set; }
    public string? Sapcode { get; set; }
    public string IsExternal { get; set; } = null!;
    public string? Billing { get; set; }
    public decimal? BillingPrice { get; set; }
    public string? EsignUserId { get; set; }
    public string? Token { get; set; }
    public string? SapcodeBdt { get; set; }
    public string? SapcodeUsd { get; set; }
    public string? Coordinate { get; set; }
    public string? Boundaries { get; set; }
    public string? IsTenant { get; set; }
    public string? ChannelId { get; set; }
    public string UsePrivy { get; set; } = null!;
    public string? SapcodeS4 { get; set; }
    public string? Skbpph { get; set; }
    public string? CompanyGroup { get; set; }
    public string? FactoryLocation { get; set; }
    public long? MasterGroupId { get; set; }
}