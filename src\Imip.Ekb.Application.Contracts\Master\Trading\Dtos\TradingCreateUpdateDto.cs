using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Master.Trading.Dtos;

public class TradingCreateUpdateDto
{
    [Required]
    public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = null!;

    public string? Address { get; set; }

    [StringLength(255)]
    public string? Npwp { get; set; }

    [Required]
    [StringLength(5)]
    public string IsActive { get; set; } = null!;

    [Required]
    public long CreatedBy { get; set; }

    public long? UpdatedBy { get; set; }
}