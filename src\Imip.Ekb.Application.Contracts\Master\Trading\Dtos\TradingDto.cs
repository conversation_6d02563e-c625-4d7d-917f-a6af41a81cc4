using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Master.Trading.Dtos;

public class TradingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string IsActive { get; set; } = string.Empty;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}