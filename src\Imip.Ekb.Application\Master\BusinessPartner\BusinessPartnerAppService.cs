using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.BusinessPartner.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.BusinessPartner;

public class BusinessPartnerAppService :
    CrudAppService<BusinessPartner, BusinessPartnerDto, Guid, PagedAndSortedResultRequestDto, BusinessPartnerCreateUpdateDto, BusinessPartnerCreateUpdateDto>,
    IBusinessPartnerAppService
{
    private readonly IBusinessPartnerRepository _businessPartnerRepository;
    private readonly BusinessPartnerMapper _mapper;
    private readonly ILogger<BusinessPartnerAppService> _logger;

    public BusinessPartnerAppService(
        IBusinessPartnerRepository businessPartnerRepository,
        BusinessPartnerMapper mapper,
        ILogger<BusinessPartnerAppService> logger)
        : base(businessPartnerRepository)
    {
        _businessPartnerRepository = businessPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<BusinessPartnerDto> CreateAsync(BusinessPartnerCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;

        await _businessPartnerRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<BusinessPartnerDto> UpdateAsync(Guid id, BusinessPartnerCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _businessPartnerRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;

        _mapper.MapToEntity(input, entity);

        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;

        await _businessPartnerRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _businessPartnerRepository.GetAsync(id);

        // Soft delete
        entity.Status = "Deleted";
        entity.UpdatedAt = Clock.Now;

        await _businessPartnerRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<BusinessPartnerDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _businessPartnerRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<BusinessPartnerDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _businessPartnerRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(BusinessPartner.DocEntry) : input.Sorting);

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<BusinessPartnerDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<BusinessPartnerDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _businessPartnerRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<BusinessPartnerDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<BusinessPartner> ApplyDynamicQuery(IQueryable<BusinessPartner> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<BusinessPartner>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<BusinessPartner>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}