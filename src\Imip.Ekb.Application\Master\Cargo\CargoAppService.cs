using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Cargo.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Cargo;

public class CargoAppService :
    CrudAppService<Cargo, CargoDto, Guid, PagedAndSortedResultRequestDto, CargoCreateUpdateDto, CargoCreateUpdateDto>,
    ICargoAppService
{
    private readonly ICargoRepository _cargoRepository;
    private readonly CargoMapper _mapper;
    private readonly ILogger<CargoAppService> _logger;

    public CargoAppService(
        ICargoRepository cargoRepository,
        CargoMapper mapper,
        ILogger<CargoAppService> logger)
        : base(cargoRepository)
    {
        _cargoRepository = cargoRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<CargoDto> CreateAsync(CargoCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;

        await _cargoRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<CargoDto> UpdateAsync(Guid id, CargoCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;

        _mapper.MapToEntity(input, entity);

        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;

        await _cargoRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);

        // Soft delete
        entity.Status = "Deleted";
        entity.UpdatedAt = Clock.Now;

        await _cargoRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<CargoDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<CargoDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Cargo.DocEntry) : input.Sorting);

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<CargoDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<CargoDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<CargoDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<Cargo> ApplyDynamicQuery(IQueryable<Cargo> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Cargo>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Cargo>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}