using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Tenant.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Tenant;

public class TenantAppService :
    CrudAppService<Tenant, MasterTenantDto, Guid, PagedAndSortedResultRequestDto, TenantCreateUpdateDto, TenantCreateUpdateDto>,
    ITenantAppService
{
    private readonly ITenantRepository _destinationPortRepository;
    private readonly TenantMapper _mapper;
    private readonly ILogger<TenantAppService> _logger;

    public TenantAppService(
        ITenantRepository destinationPortRepository,
        TenantMapper mapper,
        ILogger<TenantAppService> logger)
        : base(destinationPortRepository)
    {
        _destinationPortRepository = destinationPortRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<MasterTenantDto> CreateAsync(TenantCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _destinationPortRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<MasterTenantDto> UpdateAsync(Guid id, TenantCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _destinationPortRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        entity.UpdatedAt = Clock.Now;
        await _destinationPortRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<MasterTenantDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _destinationPortRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<MasterTenantDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _destinationPortRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Tenant.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<MasterTenantDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<MasterTenantDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _destinationPortRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<MasterTenantDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<Tenant> ApplyDynamicQuery(IQueryable<Tenant> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Tenant>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Tenant>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}