using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone.ExportVessel;
using Imip.Ekb.BoundedZone.ImportVessel;
using Imip.Ekb.BoundedZone.LocalVessel;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Cargo;
using Imip.Ekb.Master.Jetty;
using Imip.Ekb.Master.Tenant;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Vessel;

public class VesselAppService : ApplicationService, IVesselAppService
{
    private readonly IImportVesselRepository _importVesselRepository;
    private readonly IExportVesselRepository _exportVesselRepository;
    private readonly ILocalVesselRepository _localVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ITenantRepository _tenantRepository;
    private readonly ICargoRepository _cargoRepository;
    private readonly IJettyRepository _jettyRepository;
    private readonly VesselMapper _mapper;
    private readonly ILogger<VesselAppService> _logger;

    public VesselAppService(
        IImportVesselRepository importVesselRepository,
        IExportVesselRepository exportVesselRepository,
        ILocalVesselRepository localVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        ITenantRepository tenantRepository,
        ICargoRepository cargoRepository,
        IJettyRepository jettyRepository,
        VesselMapper mapper,
        ILogger<VesselAppService> logger)
    {
        _importVesselRepository = importVesselRepository;
        _exportVesselRepository = exportVesselRepository;
        _localVesselRepository = localVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _tenantRepository = tenantRepository;
        _cargoRepository = cargoRepository;
        _jettyRepository = jettyRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResultDto<VesselHeaderDto>> VesselHeadersAsync(VesselListRequestDto input)
    {
        var vesselHeaders = new List<VesselHeaderDto>();
        var totalCount = 0;

        // Use filterGroup from input, or create a new one if null
        var filterGroup = input.FilterGroup ?? new FilterGroup
        {
            Operator = LogicalOperator.And,
            Conditions = new List<FilterCondition>()
        };

        // Split filterGroup into header and item filters
        var (headerFilterGroup, itemFilterGroup) = SplitHeaderAndItemFilters(filterGroup);

        // If there are item filters, find matching DocEntry values
        List<int> matchingDocEntries = null;
        if (itemFilterGroup.Conditions.Any())
        {
            var zoneDetailQuery = await _zoneDetailRepository.GetQueryableAsync();
            zoneDetailQuery = DynamicQueryBuilder<ZoneDetail>.ApplyFilters(zoneDetailQuery, itemFilterGroup);
            matchingDocEntries = await AsyncExecuter.ToListAsync(zoneDetailQuery.Select(z => z.DocNum.Value).Distinct());
        }

        // Remove vesselType filter for header queries (handled by repo selection)
        headerFilterGroup.Conditions = headerFilterGroup.Conditions
            .Where(c => !string.Equals(c.FieldName, "vesselType", StringComparison.OrdinalIgnoreCase))
            .ToList();

        var parameters = new QueryParametersDto
        {
            MaxResultCount = input.MaxResultCount,
            SkipCount = input.SkipCount,
            Sorting = input.Sorting,
            Sort = new List<SortInfo>(),
            FilterGroup = headerFilterGroup
        };

        // Import
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "Import")
        {
            var importQuery = await _importVesselRepository.GetQueryableAsync();
            importQuery = ApplyDynamicQuery(importQuery, parameters);
            if (matchingDocEntries != null)
                importQuery = importQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(importQuery);

            var importVessels = await AsyncExecuter.ToListAsync(
                importQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in importVessels)
            {
                var header = _mapper.MapImportVesselToHeaderDtoWithType(vessel);
                if (vessel.JettyId.HasValue)
                {
                    var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == vessel.JettyId.Value);
                    if (jetty != null)
                        header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
                }
                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, "Import");
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        // Export
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "Export")
        {
            var exportQuery = await _exportVesselRepository.GetQueryableAsync();
            exportQuery = ApplyDynamicQuery(exportQuery, parameters);
            if (matchingDocEntries != null)
                exportQuery = exportQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(exportQuery);

            var exportVessels = await AsyncExecuter.ToListAsync(
                exportQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in exportVessels)
            {
                var header = _mapper.MapExportVesselToHeaderDtoWithType(vessel);
                if (vessel.VesselName > 0)
                {
                    var cargoQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
                    var cargo = await AsyncExecuter.FirstOrDefaultAsync(cargoQuery.Where(c => c.DocEntry == vessel.VesselName));
                    if (cargo != null)
                    {
                        header.Cargo = _mapper.MapCargoToCargoShortDto(cargo);
                        header.VesselName = cargo.Name;
                    }
                }
                if (vessel.JettyId.HasValue)
                {
                    var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == vessel.JettyId.Value);
                    if (jetty != null)
                        header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
                }
                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, "Export");
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        // Local
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "LocalIn" || input.VesselType == "LocalOut")
        {
            var localQuery = await _localVesselRepository.GetQueryableAsync();
            if (input.VesselType == "LocalIn")
                localQuery = localQuery.Where(v => v.TransType == "IN");
            else if (input.VesselType == "LocalOut")
                localQuery = localQuery.Where(v => v.TransType == "OUT");
            localQuery = ApplyDynamicQuery(localQuery, parameters);
            if (matchingDocEntries != null)
                localQuery = localQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(localQuery);

            var localVessels = await AsyncExecuter.ToListAsync(
                localQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in localVessels)
            {
                var header = _mapper.MapLocalVesselToHeaderDtoWithType(vessel);
                if (vessel.VesselName > 0)
                {
                    var cargoQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
                    var cargo = await AsyncExecuter.FirstOrDefaultAsync(cargoQuery.Where(c => c.DocEntry == vessel.VesselName));
                    if (cargo != null)
                    {
                        header.Cargo = _mapper.MapCargoToCargoShortDto(cargo);
                        header.VesselName = cargo.Name;
                    }
                }
                if (vessel.BargeId.HasValue)
                {
                    var bargeQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
                    var barge = await AsyncExecuter.FirstOrDefaultAsync(bargeQuery.Where(c => c.DocEntry == vessel.BargeId.Value));
                    if (barge != null)
                        header.Barge = _mapper.MapCargoToCargoShortDto(barge);
                }
                if (vessel.JettyId.HasValue)
                {
                    var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == vessel.JettyId.Value);
                    if (jetty != null)
                        header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
                }
                var vesselType = vessel.VesselType == "IN" ? "LocalIn" : "LocalOut";
                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, vesselType);
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        return new PagedResultDto<VesselHeaderDto>(totalCount, vesselHeaders);
    }

    public async Task<PagedResultDto<VesselItemDto>> VesselItemsAsync(VesselListRequestDto input)
    {
        // Convert VesselListRequestDto to QueryParametersDto for DynamicQueryBuilder
        var parameters = new QueryParametersDto
        {
            MaxResultCount = input.MaxResultCount,
            SkipCount = input.SkipCount,
            Sorting = input.Sorting,
            Sort = new List<SortInfo>(),
            FilterGroup = new FilterGroup
            {
                Operator = LogicalOperator.And,
                Conditions = new List<FilterCondition>()
            }
        };

        // Add date filters if specified
        if (input.FromDate.HasValue)
        {
            parameters.FilterGroup.Conditions.Add(new FilterCondition
            {
                FieldName = "CreationTime",
                Operator = FilterOperator.GreaterThanOrEqual,
                Value = input.FromDate.Value
            });
        }

        if (input.ToDate.HasValue)
        {
            parameters.FilterGroup.Conditions.Add(new FilterCondition
            {
                FieldName = "CreationTime",
                Operator = FilterOperator.LessThanOrEqual,
                Value = input.ToDate.Value
            });
        }

        // Get ZoneDetail items with tenant information
        var zoneDetailQuery = await _zoneDetailRepository.GetQueryableAsync();
        zoneDetailQuery = ApplyDynamicQuery(zoneDetailQuery, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(zoneDetailQuery);

        // Get ZoneDetail items
        var zoneDetails = await AsyncExecuter.ToListAsync(
            zoneDetailQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
        );

        var vesselItems = new List<VesselItemDto>();

        // Map to DTOs with vessel type determination and tenant information
        foreach (var zoneDetail in zoneDetails)
        {
            var vesselType = DetermineVesselTypeFromZoneDetail(zoneDetail);
            var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

            // Get tenant information if TenantKey is available
            if (zoneDetail.TenantKey.HasValue)
            {
                var tenant = await _tenantRepository.FindAsync(t => t.DocEntry == zoneDetail.TenantKey.Value);
                if (tenant != null)
                {
                    itemDto.Tenant = _mapper.MapTenantToTenantShortDto(tenant);
                }
            }

            vesselItems.Add(itemDto);
        }

        return new PagedResultDto<VesselItemDto>(totalCount, vesselItems);
    }

    public async Task<VesselHeaderDto> VesselHeaderAsync(Guid id, string vesselType)
    {
        var header = vesselType switch
        {
            "Import" => await GetImportVesselHeaderAsync(id),
            "Export" => await GetExportVesselHeaderAsync(id),
            "LocalIn" or "LocalOut" => await GetLocalVesselHeaderAsync(id),
            _ => throw new ArgumentException($"Invalid vessel type: {vesselType}")
        };

        // Get the DocEntry from the header to fetch related items
        var docEntry = header.DocEntry;
        header.Items = await GetVesselItemsForHeaderAsync(docEntry, vesselType);

        return header;
    }

    public async Task<VesselItemDto> VesselItemAsync(Guid id, string vesselType)
    {
        var zoneDetail = await _zoneDetailRepository.GetAsync(id);
        var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

        // Get tenant information if TenantKey is available
        if (zoneDetail.TenantKey.HasValue)
        {
            var tenant = await _tenantRepository.FindAsync(t => t.DocEntry == zoneDetail.TenantKey.Value);
            if (tenant != null)
            {
                itemDto.Tenant = _mapper.MapTenantToTenantShortDto(tenant);
            }
        }

        return itemDto;
    }

    private async Task<List<VesselItemDto>> GetVesselItemsForHeaderAsync(int docEntry, string vesselType)
    {
        // Query ZoneDetail items that are related to this vessel
        var zoneDetailQuery = await _zoneDetailRepository.GetOptimizedQueryableAsync();

        // 1. First try DocNum relationship (current approach)
        var docNumItems = await AsyncExecuter.ToListAsync(
            zoneDetailQuery.Where(z => z.DocNum == docEntry)
        );

        var items = new List<VesselItemDto>();
        foreach (var zoneDetail in docNumItems)
        {
            var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

            // Get tenant information if TenantKey is available
            if (zoneDetail.TenantKey.HasValue)
            {
                var tenant = await _tenantRepository.FindAsync(t => t.DocEntry == zoneDetail.TenantKey.Value);
                if (tenant != null)
                {
                    itemDto.Tenant = _mapper.MapTenantToTenantShortDto(tenant);
                }
            }

            items.Add(itemDto);
        }

        return items;
    }

    private async Task<VesselHeaderDto> GetImportVesselHeaderAsync(Guid id)
    {
        var entity = await _importVesselRepository.GetAsync(id);
        var header = _mapper.MapImportVesselToHeaderDtoWithType(entity);

        // Get jetty information if JettyId is available
        if (entity.JettyId.HasValue)
        {
            var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == entity.JettyId.Value);
            if (jetty != null)
            {
                header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
            }
        }

        return header;
    }

    private async Task<VesselHeaderDto> GetExportVesselHeaderAsync(Guid id)
    {
        var entity = await _exportVesselRepository.GetAsync(id);
        var header = _mapper.MapExportVesselToHeaderDtoWithType(entity);

        // Get cargo information if VesselName references Cargo
        if (entity.VesselName > 0)
        {
            var cargoQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
            var cargo = await AsyncExecuter.FirstOrDefaultAsync(cargoQuery.Where(c => c.DocEntry == entity.VesselName));
            if (cargo != null)
            {
                header.Cargo = _mapper.MapCargoToCargoShortDto(cargo);
                header.VesselName = cargo.Name; // Use cargo name as vessel name
            }
        }

        // Get jetty information if JettyId is available
        if (entity.JettyId.HasValue)
        {
            var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == entity.JettyId.Value);
            if (jetty != null)
            {
                header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
            }
        }

        return header;
    }

    private async Task<VesselHeaderDto> GetLocalVesselHeaderAsync(Guid id)
    {
        var entity = await _localVesselRepository.GetAsync(id);
        var header = _mapper.MapLocalVesselToHeaderDtoWithType(entity);

        // Get cargo information if VesselName references Cargo
        if (entity.VesselName > 0)
        {
            var cargoQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
            var cargo = await AsyncExecuter.FirstOrDefaultAsync(cargoQuery.Where(c => c.DocEntry == entity.VesselName));
            if (cargo != null)
            {
                header.Cargo = _mapper.MapCargoToCargoShortDto(cargo);
                header.VesselName = cargo.Name; // Use cargo name as vessel name
            }
        }

        // Get barge information if BargeId references Cargo
        if (entity.BargeId.HasValue)
        {
            var bargeQuery = await _cargoRepository.GetQueryableWithSoftDeleteFilterAsync();
            var barge = await AsyncExecuter.FirstOrDefaultAsync(bargeQuery.Where(c => c.DocEntry == entity.BargeId.Value));
            if (barge != null)
            {
                header.Barge = _mapper.MapCargoToCargoShortDto(barge);
            }
        }

        // Get jetty information if JettyId is available
        if (entity.JettyId.HasValue)
        {
            var jetty = await _jettyRepository.FindAsync(j => j.DocEntry == entity.JettyId.Value);
            if (jetty != null)
            {
                header.Jetty = _mapper.MapJettyToJettyShortDto(jetty);
            }
        }

        return header;
    }

    private IQueryable<T> ApplyDynamicQuery<T>(IQueryable<T> query, QueryParametersDto parameters) where T : class
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<T>.ApplyFilters(query, parameters.FilterGroup);
        }

        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<T>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Parse sorting direction from the sorting string
            var sortingParts = parameters.Sorting.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var sortField = sortingParts[0];
            var isDescending = sortingParts.Length > 1 && sortingParts[1].ToLowerInvariant() == "desc";

            query = DynamicQueryBuilder<T>.ApplySorting(query, sortField, isDescending);
        }
        else
        {
            // Default sorting by DocEntry descending
            query = DynamicQueryBuilder<T>.ApplySorting(query, "DocEntry", true);
        }

        return query;
    }

    private string DetermineVesselTypeFromZoneDetail(ZoneDetail zoneDetail)
    {
        // This is a simplified logic - you might need to implement more sophisticated logic
        // based on your business rules to determine vessel type from ZoneDetail
        if (!string.IsNullOrEmpty(zoneDetail.DocType))
        {
            return zoneDetail.DocType.ToUpper() switch
            {
                "IMPORT" => "Import",
                "EXPORT" => "Export",
                "LOCAL" => "LocalIn", // Default to LocalIn, you might need more logic
                _ => "Import" // Default fallback
            };
        }

        return "Import"; // Default fallback
    }

    // Helper: Split filterGroup into header and item filters
    private (FilterGroup header, FilterGroup item) SplitHeaderAndItemFilters(FilterGroup filterGroup)
    {
        var headerConditions = new List<FilterCondition>();
        var itemConditions = new List<FilterCondition>();
        foreach (var cond in filterGroup.Conditions)
        {
            if (cond.FieldName.StartsWith("items.", StringComparison.OrdinalIgnoreCase))
                itemConditions.Add(new FilterCondition
                {
                    FieldName = cond.FieldName.Substring(6), // remove 'items.'
                    Operator = cond.Operator,
                    Value = cond.Value
                });
            else
                headerConditions.Add(cond);
        }
        return (
            new FilterGroup { Operator = filterGroup.Operator, Conditions = headerConditions },
            new FilterGroup { Operator = filterGroup.Operator, Conditions = itemConditions }
        );
    }

    // Helper: Check if a VesselItemDto matches the item filter group
    private bool ItemMatchesFilter(VesselItemDto item, FilterGroup filterGroup)
    {
        foreach (var cond in filterGroup.Conditions)
        {
            var value = cond.FieldName.ToLower() switch
            {
                "tenant.name" => item.Tenant?.Name,
                "itemname" => item.ItemName,
                "remarks" => item.Remarks,
                "cargo" => item.Cargo,
                "shipment" => item.Shipment,
                _ => null
            };
            if (value == null) return false;
            if (cond.Operator == FilterOperator.Contains && !value.Contains(cond.Value?.ToString(), StringComparison.OrdinalIgnoreCase))
                return false;
            if (cond.Operator == FilterOperator.Equals && value != cond.Value?.ToString())
                return false;
        }
        return true;
    }
}