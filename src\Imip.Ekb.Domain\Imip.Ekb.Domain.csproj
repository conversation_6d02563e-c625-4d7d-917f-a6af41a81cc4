﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.Ekb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.Ekb.Domain.Shared\Imip.Ekb.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="SilkierQuartz.Plugins.RecentHistory" Version="9.0.391" />
    <PackageReference Include="Volo.Abp.Emailing" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Caching" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Quartz" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain" Version="9.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Domain" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="9.1.1" />
  </ItemGroup>

</Project>
