using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Account.Web;
using Volo.Abp.Account.Web.Pages.Account;
using Volo.Abp.Identity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Validation;
using Imip.Ekb.Web.Services.Interfaces;
using System.Collections.Generic;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.Cookies;
using Volo.Abp.Data;

namespace Imip.Ekb.Web.Pages.Account
{
    // This class inherits from the ABP's LoginModel to maintain all the functionality
    public class LoginModel : Volo.Abp.Account.Web.Pages.Account.LoginModel
    {
        private readonly ILogger<LoginModel> _logger;
        private readonly ITokenService _tokenService;
        private readonly IUserSynchronizationService _userSyncService;

        public LoginModel(
            IAuthenticationSchemeProvider schemeProvider,
            IOptions<AbpAccountOptions> accountOptions,
            IOptions<IdentityOptions> identityOptions,
            IdentityDynamicClaimsPrincipalContributorCache identityDynamicClaimsPrincipalContributorCache,
            ILogger<LoginModel> logger,
            ITokenService tokenService,
            IUserSynchronizationService userSyncService)
            : base(schemeProvider, accountOptions, identityOptions, identityDynamicClaimsPrincipalContributorCache)
        {
            _logger = logger;
            _tokenService = tokenService;
            _userSyncService = userSyncService;
        }

        // Override the OnGetAsync method to maintain the original functionality
        public override async Task<IActionResult> OnGetAsync()
        {
            return await base.OnGetAsync();
        }

        // Override the OnPostAsync method with custom error handling
        public override async Task<IActionResult> OnPostAsync(string action)
        {
            try
            {
                _logger.LogInformation("Login attempt with username: {Username}", LoginInput?.UserNameOrEmailAddress);

                if (!ModelState.IsValid)
                {
                    foreach (var state in ModelState)
                    {
                        foreach (var error in state.Value.Errors)
                        {
                            _logger.LogWarning("Validation error for {Field}: {Error}", state.Key, error.ErrorMessage);
                        }
                    }
                    return Page();
                }

                // Use ROPC flow for login
                var tokenResult = await _tokenService.PasswordLoginAsync(LoginInput.UserNameOrEmailAddress, LoginInput.Password);
                if (tokenResult == null || !string.IsNullOrEmpty(tokenResult.Error))
                {
                    _logger.LogWarning("ROPC login failed: {Error}", tokenResult?.ErrorDescription ?? "Unknown error");
                    ModelState.AddModelError(string.Empty, tokenResult?.ErrorDescription ?? "Login failed. Please check your credentials.");
                    return Page();
                }

                // Synchronize user from JWT access token
                var user = await _userSyncService.SynchronizeUserFromTokenAsync(tokenResult.AccessToken);
                if (user == null)
                {
                    _logger.LogError("User synchronization failed after ROPC login");
                    ModelState.AddModelError(string.Empty, "User synchronization failed. Please contact support.");
                    return Page();
                }

                // Create claims principal for the user
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Name, user.UserName),
                    new Claim(ClaimTypes.Email, user.Email ?? "")
                };
                if (!string.IsNullOrEmpty(user.Name))
                    claims.Add(new Claim("given_name", user.Name));
                if (!string.IsNullOrEmpty(user.Surname))
                    claims.Add(new Claim("family_name", user.Surname));
                if (user.TenantId.HasValue)
                    claims.Add(new Claim("tenant_id", user.TenantId.Value.ToString()));

                var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var principal = new ClaimsPrincipal(identity);

                // Sign in the user (set auth cookie)
                await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);

                // Optionally store tokens in user properties for refresh, etc.
                user.SetProperty("AccessToken", tokenResult.AccessToken);
                if (!string.IsNullOrEmpty(tokenResult.RefreshToken))
                    user.SetProperty("RefreshToken", tokenResult.RefreshToken);
                await _userSyncService.CreateOrUpdateUserAsync(new UserSynchronizationInfo
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    Email = user.Email,
                    Name = user.Name,
                    Surname = user.Surname,
                    TenantId = user.TenantId
                });

                // Redirect to home or return URL
                return LocalRedirect(ReturnUrl ?? "/");
            }
            catch (AbpValidationException ex)
            {
                _logger.LogError(ex, "Validation error during login");
                foreach (var error in ex.ValidationErrors)
                {
                    var memberName = error.MemberNames.Any() ? error.MemberNames.First() : string.Empty;
                    ModelState.AddModelError(memberName, error.ErrorMessage);
                }
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                ModelState.AddModelError(string.Empty, "An error occurred during login. Please try again.");
                return Page();
            }
        }
    }
}
