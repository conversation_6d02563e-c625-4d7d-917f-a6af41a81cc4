using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Imip.Ekb.Web.Services.Interfaces;

public interface ITokenService
{
    /// <summary>
    /// Gets the current user's access token
    /// </summary>
    Task<string> GetAccessTokenAsync();

    /// <summary>
    /// Gets the current user's refresh token
    /// </summary>
    Task<string> GetRefreshTokenAsync();

    /// <summary>
    /// Gets a valid access token, refreshing if necessary
    /// </summary>
    Task<string> GetValidAccessTokenAsync();

    /// <summary>
    /// Refreshes the access token using the refresh token
    /// </summary>
    Task<string> RefreshAccessTokenAsync();

    /// <summary>
    /// Checks if the current user has a valid access token
    /// </summary>
    Task<bool> HasValidTokenAsync();

    /// <summary>
    /// Checks if refresh token is expired
    /// </summary>
    Task<bool> IsRefreshTokenExpiredAsync();

    /// <summary>
    /// Performs Resource Owner Password Credentials (ROPC) login and returns the token response
    /// </summary>
    Task<TokenResponse?> PasswordLoginAsync(string username, string password);
}

public interface IAuthenticationTokenValidationService
{
    /// <summary>
    /// Validates JWT Bearer token and enriches claims with ABP-specific claims
    /// </summary>
    Task ValidateJwtBearerTokenAsync(Microsoft.AspNetCore.Authentication.JwtBearer.TokenValidatedContext context);

    /// <summary>
    /// Validates OpenID Connect token and creates/updates user in ABP system
    /// </summary>
    Task ValidateOpenIdConnectTokenAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context);
}

/// <summary>
/// DTO for token response from ROPC login
/// </summary>
public class TokenResponse
{
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public string? TokenType { get; set; }
    public int ExpiresIn { get; set; }
    public string? Error { get; set; }
    public string? ErrorDescription { get; set; }
}